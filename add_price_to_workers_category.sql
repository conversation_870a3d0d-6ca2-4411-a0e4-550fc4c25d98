-- Add essential price field to tabl_workers_category table
-- This is the minimal necessary addition for pricing functionality

ALTER TABLE `tabl_workers_category` 
ADD COLUMN `price` decimal(10,2) DEFAULT NULL COMMENT 'Service price for this category';

-- Add index for better performance
ALTER TABLE `tabl_workers_category`
ADD INDEX `idx_price` (`price`);

-- Sample data update (optional - you can set prices manually)
-- UPDATE `tabl_workers_category` SET `price` = 500.00 WHERE `id` = 16;
-- UPDATE `tabl_workers_category` SET `price` = 800.00 WHERE `id` = 17;

-- Price field added successfully
-- Now workers can have specific prices for their category services
