<?php
session_start();
include('../lib/db_connection.php');
include('../lib/auth.php');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['booking_id'])) {
    echo '<div class="alert alert-danger">Invalid request</div>';
    exit;
}

$booking_id = intval($_POST['booking_id']);

$query = dbQuery("SELECT b.*, 
                         u.fullname as customer_name, u.phone_number as customer_phone, u.email as customer_email,
                         w.name as worker_name, w.phone as worker_phone, w.city as worker_city, w.state as worker_state,
                         c.cat_name, c.cat_icon,
                         wc.worker_image, wc.description as worker_description, wc.price, wc.price_type, wc.experience
                  FROM tabl_booking b
                  LEFT JOIN tabl_users u ON b.user_id = u.user_id
                  LEFT JOIN tabl_workers w ON b.worker_id = w.worker_id
                  LEFT JOIN tabl_categories c ON b.cat_id = c.cat_id
                  LEFT JOIN tabl_workers_category wc ON b.worker_category_id = wc.id
                  WHERE b.id = '$booking_id'");

if (dbNumRows($query) == 0) {
    echo '<div class="alert alert-danger">Booking not found</div>';
    exit;
}

$booking = dbFetchAssoc($query);

$statusLabels = [
    0 => '<span class="badge badge-warning">Pending</span>',
    1 => '<span class="badge badge-info">Confirmed</span>',
    2 => '<span class="badge badge-success">Completed</span>',
    3 => '<span class="badge badge-danger">Cancelled</span>'
];

// Get state name
$stateName = '';
if (!empty($booking['worker_state'])) {
    $stateQuery = dbQuery("SELECT state_name FROM tabl_states WHERE state_id = '" . $booking['worker_state'] . "'");
    if (dbNumRows($stateQuery) > 0) {
        $stateData = dbFetchAssoc($stateQuery);
        $stateName = $stateData['state_name'];
    }
}
?>

<div class="row">
    <div class="col-md-6">
        <h6 class="text-primary"><i class="fas fa-user"></i> Customer Information</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Name:</strong></td>
                <td><?= htmlspecialchars($booking['customer_name'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Phone:</strong></td>
                <td><?= htmlspecialchars($booking['customer_phone'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Email:</strong></td>
                <td><?= htmlspecialchars($booking['customer_email'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>User Phone:</strong></td>
                <td><?= htmlspecialchars($booking['user_phone'] ?? 'N/A') ?></td>
            </tr>
        </table>
    </div>
    
    <div class="col-md-6">
        <h6 class="text-primary"><i class="fas fa-user-tie"></i> Worker Information</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Name:</strong></td>
                <td><?= htmlspecialchars($booking['worker_name'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Phone:</strong></td>
                <td><?= htmlspecialchars($booking['worker_phone'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Location:</strong></td>
                <td><?= htmlspecialchars($booking['worker_city'] ?? 'N/A') ?><?= $stateName ? ', ' . $stateName : '' ?></td>
            </tr>
            <tr>
                <td><strong>Experience:</strong></td>
                <td><?= htmlspecialchars($booking['experience'] ?? 'N/A') ?> years</td>
            </tr>
        </table>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-6">
        <h6 class="text-primary"><i class="fas fa-calendar"></i> Booking Details</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Category:</strong></td>
                <td><?= htmlspecialchars($booking['cat_name'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Booking Date:</strong></td>
                <td><?= date('d/m/Y', strtotime($booking['booking_date'])) ?></td>
            </tr>
            <tr>
                <td><strong>Booking Time:</strong></td>
                <td><?= date('h:i A', strtotime($booking['booking_time'])) ?></td>
            </tr>
            <tr>
                <td><strong>Status:</strong></td>
                <td><?= $statusLabels[$booking['status']] ?></td>
            </tr>
        </table>
    </div>
    
    <div class="col-md-6">
        <h6 class="text-primary"><i class="fas fa-rupee-sign"></i> Pricing Information</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Worker Rate:</strong></td>
                <td>
                    <?php if ($booking['price']): ?>
                        ₹<?= number_format($booking['price'], 2) ?>
                        <?php if ($booking['price_type']): ?>
                            (<?= ucfirst($booking['price_type']) ?>)
                        <?php endif; ?>
                    <?php else: ?>
                        N/A
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>Total Price:</strong></td>
                <td>
                    <?php if ($booking['total_price']): ?>
                        <strong class="text-success">₹<?= number_format($booking['total_price'], 2) ?></strong>
                    <?php else: ?>
                        <span class="text-muted">Not set</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>Created:</strong></td>
                <td><?= date('d/m/Y h:i A', strtotime($booking['created_at'])) ?></td>
            </tr>
            <?php if ($booking['completed_at']): ?>
            <tr>
                <td><strong>Completed:</strong></td>
                <td><?= date('d/m/Y h:i A', strtotime($booking['completed_at'])) ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
</div>

<?php if ($booking['user_address']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-map-marker-alt"></i> Service Address</h6>
        <div class="alert alert-light">
            <?= nl2br(htmlspecialchars($booking['user_address'])) ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($booking['service_description']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-file-text"></i> Service Description</h6>
        <div class="alert alert-info">
            <?= nl2br(htmlspecialchars($booking['service_description'])) ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($booking['worker_notes']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-comment"></i> Worker Notes</h6>
        <div class="alert alert-warning">
            <?= nl2br(htmlspecialchars($booking['worker_notes'])) ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($booking['completion_notes']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-check-circle"></i> Completion Notes</h6>
        <div class="alert alert-success">
            <?= nl2br(htmlspecialchars($booking['completion_notes'])) ?>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row mt-4">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-cogs"></i> Admin Actions</h6>
        <div class="btn-group" role="group">
            <?php if ($booking['status'] == 0): ?>
            <button type="button" class="btn btn-success" onclick="updateBookingStatusFromModal(<?= $booking['id'] ?>, 1)">
                <i class="fas fa-check"></i> Confirm Booking
            </button>
            <?php endif; ?>
            
            <?php if ($booking['status'] == 1): ?>
            <button type="button" class="btn btn-primary" onclick="updateBookingStatusFromModal(<?= $booking['id'] ?>, 2)">
                <i class="fas fa-star"></i> Mark Completed
            </button>
            <?php endif; ?>
            
            <?php if ($booking['status'] != 3): ?>
            <button type="button" class="btn btn-danger" onclick="updateBookingStatusFromModal(<?= $booking['id'] ?>, 3)">
                <i class="fas fa-times"></i> Cancel Booking
            </button>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function updateBookingStatusFromModal(bookingId, status) {
    var statusText = {
        1: 'confirm',
        2: 'mark as completed',
        3: 'cancel'
    };
    
    var confirmMessage = "Are you sure you want to " + statusText[status] + " this booking?";
    
    if (confirm(confirmMessage)) {
        $.ajax({
            url: 'ajax/update_booking_status.php',
            type: 'POST',
            data: { 
                booking_id: bookingId,
                status: status
            },
            success: function(response) {
                if (response == 1) {
                    $('#bookingDetailsModal').modal('hide');
                    location.reload();
                } else {
                    alert('Error updating booking status');
                }
            },
            error: function() {
                alert('Error updating booking status');
            }
        });
    }
}
</script>
