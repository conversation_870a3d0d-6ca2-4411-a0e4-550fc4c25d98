<?php
session_start();
include('../lib/db_connection.php');
include('../lib/auth.php');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['booking_id'])) {
    echo '<div class="alert alert-danger">Invalid request</div>';
    exit;
}

$booking_id = intval($_POST['booking_id']);

$query = dbQuery("SELECT bb.*, 
                         u.fullname as customer_name, u.phone_number as customer_phone, u.email as customer_email,
                         c.cat_name, c.cat_icon
                  FROM tabl_bulk_booking bb
                  LEFT JOIN tabl_users u ON bb.user_id = u.user_id
                  LEFT JOIN tabl_categories c ON bb.cat_id = c.cat_id
                  WHERE bb.id = '$booking_id'");

if (dbNumRows($query) == 0) {
    echo '<div class="alert alert-danger">Bulk booking request not found</div>';
    exit;
}

$booking = dbFetchAssoc($query);

$statusLabels = [
    0 => '<span class="badge badge-warning">Pending Review</span>',
    1 => '<span class="badge badge-info">Under Review</span>',
    2 => '<span class="badge badge-primary">Quote Provided</span>',
    3 => '<span class="badge badge-success">Quote Accepted</span>',
    4 => '<span class="badge badge-secondary">In Progress</span>',
    5 => '<span class="badge badge-success">Completed</span>',
    6 => '<span class="badge badge-danger">Cancelled</span>'
];

// Get state name
$stateName = '';
if (!empty($booking['state'])) {
    $stateQuery = dbQuery("SELECT state_name FROM tabl_states WHERE state_id = '" . $booking['state'] . "'");
    if (dbNumRows($stateQuery) > 0) {
        $stateData = dbFetchAssoc($stateQuery);
        $stateName = $stateData['state_name'];
    }
}
?>

<div class="row">
    <div class="col-md-6">
        <h6 class="text-primary"><i class="fas fa-user"></i> Customer Information</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Name:</strong></td>
                <td><?= htmlspecialchars($booking['customer_name'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Phone:</strong></td>
                <td><?= htmlspecialchars($booking['customer_phone'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Email:</strong></td>
                <td><?= htmlspecialchars($booking['customer_email'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Contact Person:</strong></td>
                <td><?= htmlspecialchars($booking['contact_person']) ?></td>
            </tr>
            <tr>
                <td><strong>Contact Phone:</strong></td>
                <td><?= htmlspecialchars($booking['contact_phone']) ?></td>
            </tr>
            <?php if ($booking['contact_email']): ?>
            <tr>
                <td><strong>Contact Email:</strong></td>
                <td><?= htmlspecialchars($booking['contact_email']) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['alternative_phone']): ?>
            <tr>
                <td><strong>Alt Phone:</strong></td>
                <td><?= htmlspecialchars($booking['alternative_phone']) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['preferred_contact_time']): ?>
            <tr>
                <td><strong>Preferred Contact:</strong></td>
                <td><?= ucfirst($booking['preferred_contact_time']) ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
    
    <div class="col-md-6">
        <h6 class="text-primary"><i class="fas fa-clipboard-list"></i> Project Information</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Category:</strong></td>
                <td><?= htmlspecialchars($booking['cat_name'] ?? 'N/A') ?></td>
            </tr>
            <tr>
                <td><strong>Workers Needed:</strong></td>
                <td><?= $booking['required_workers'] ?></td>
            </tr>
            <?php if ($booking['required_experience']): ?>
            <tr>
                <td><strong>Experience Level:</strong></td>
                <td><?= ucfirst($booking['required_experience']) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['work_duration']): ?>
            <tr>
                <td><strong>Duration:</strong></td>
                <td><?= htmlspecialchars($booking['work_duration']) ?></td>
            </tr>
            <?php endif; ?>
            <tr>
                <td><strong>Urgency:</strong></td>
                <td>
                    <span class="badge badge-<?= $booking['urgency_level'] == 'urgent' ? 'danger' : ($booking['urgency_level'] == 'high' ? 'warning' : 'info') ?>">
                        <?= ucfirst($booking['urgency_level']) ?>
                    </span>
                </td>
            </tr>
            <tr>
                <td><strong>Status:</strong></td>
                <td><?= $statusLabels[$booking['status']] ?></td>
            </tr>
        </table>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-6">
        <h6 class="text-primary"><i class="fas fa-calendar"></i> Timeline</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Request Date:</strong></td>
                <td><?= date('d/m/Y h:i A', strtotime($booking['created_at'])) ?></td>
            </tr>
            <?php if ($booking['start_date']): ?>
            <tr>
                <td><strong>Preferred Start:</strong></td>
                <td><?= date('d/m/Y', strtotime($booking['start_date'])) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['end_date']): ?>
            <tr>
                <td><strong>Expected End:</strong></td>
                <td><?= date('d/m/Y', strtotime($booking['end_date'])) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['quote_date']): ?>
            <tr>
                <td><strong>Quote Date:</strong></td>
                <td><?= date('d/m/Y h:i A', strtotime($booking['quote_date'])) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['accepted_date']): ?>
            <tr>
                <td><strong>Accepted Date:</strong></td>
                <td><?= date('d/m/Y h:i A', strtotime($booking['accepted_date'])) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['completed_date']): ?>
            <tr>
                <td><strong>Completed Date:</strong></td>
                <td><?= date('d/m/Y h:i A', strtotime($booking['completed_date'])) ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
    
    <div class="col-md-6">
        <h6 class="text-primary"><i class="fas fa-rupee-sign"></i> Budget Information</h6>
        <table class="table table-sm">
            <?php if ($booking['budget_per_worker']): ?>
            <tr>
                <td><strong>Budget per Worker:</strong></td>
                <td>₹<?= number_format($booking['budget_per_worker'], 2) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['total_budget']): ?>
            <tr>
                <td><strong>Total Budget:</strong></td>
                <td>₹<?= number_format($booking['total_budget'], 2) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['quoted_amount']): ?>
            <tr>
                <td><strong>Quoted Amount:</strong></td>
                <td><strong class="text-success">₹<?= number_format($booking['quoted_amount'], 2) ?></strong></td>
            </tr>
            <?php endif; ?>
            <?php if ($booking['quoted_workers']): ?>
            <tr>
                <td><strong>Quoted Workers:</strong></td>
                <td><?= $booking['quoted_workers'] ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-6">
        <h6 class="text-primary"><i class="fas fa-map-marker-alt"></i> Location Information</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Location Type:</strong></td>
                <td><?= ucfirst($booking['location_type']) ?> Location</td>
            </tr>
            <tr>
                <td><strong>City:</strong></td>
                <td><?= htmlspecialchars($booking['city']) ?></td>
            </tr>
            <tr>
                <td><strong>State:</strong></td>
                <td><?= $stateName ?: 'N/A' ?></td>
            </tr>
            <tr>
                <td><strong>Pincode:</strong></td>
                <td><?= htmlspecialchars($booking['pincode']) ?></td>
            </tr>
        </table>
    </div>
</div>

<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-file-text"></i> Project Description</h6>
        <div class="alert alert-light">
            <strong><?= htmlspecialchars($booking['work_title']) ?></strong><br><br>
            <?= nl2br(htmlspecialchars($booking['work_description'])) ?>
        </div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-home"></i> Work Address</h6>
        <div class="alert alert-info">
            <strong>Primary Address:</strong><br>
            <?= nl2br(htmlspecialchars($booking['primary_address'])) ?>
            
            <?php if ($booking['additional_addresses']): ?>
            <br><br><strong>Additional Addresses:</strong><br>
            <?= nl2br(htmlspecialchars($booking['additional_addresses'])) ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if ($booking['special_requirements']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-list-ul"></i> Special Requirements</h6>
        <div class="alert alert-warning">
            <?= nl2br(htmlspecialchars($booking['special_requirements'])) ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($booking['quote_details']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-file-invoice-dollar"></i> Quote Details</h6>
        <div class="alert alert-success">
            <?= nl2br(htmlspecialchars($booking['quote_details'])) ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($booking['admin_notes']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-sticky-note"></i> Admin Notes</h6>
        <div class="alert alert-secondary">
            <?= nl2br(htmlspecialchars($booking['admin_notes'])) ?>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row mt-4">
    <div class="col-12">
        <h6 class="text-primary"><i class="fas fa-cogs"></i> Admin Actions</h6>
        <div class="btn-group" role="group">
            <?php if ($booking['status'] == 0): ?>
            <button type="button" class="btn btn-warning" onclick="updateBulkBookingStatusFromModal(<?= $booking['id'] ?>, 1)">
                <i class="fas fa-search"></i> Mark Under Review
            </button>
            <?php endif; ?>
            
            <?php if ($booking['status'] <= 1): ?>
            <button type="button" class="btn btn-primary" onclick="provideQuoteFromModal(<?= $booking['id'] ?>)">
                <i class="fas fa-file-invoice-dollar"></i> Provide Quote
            </button>
            <?php endif; ?>
            
            <?php if ($booking['status'] == 3): ?>
            <button type="button" class="btn btn-secondary" onclick="updateBulkBookingStatusFromModal(<?= $booking['id'] ?>, 4)">
                <i class="fas fa-play"></i> Mark In Progress
            </button>
            <?php endif; ?>
            
            <?php if ($booking['status'] == 4): ?>
            <button type="button" class="btn btn-success" onclick="updateBulkBookingStatusFromModal(<?= $booking['id'] ?>, 5)">
                <i class="fas fa-check"></i> Mark Completed
            </button>
            <?php endif; ?>
            
            <?php if ($booking['status'] != 6 && $booking['status'] != 5): ?>
            <button type="button" class="btn btn-danger" onclick="updateBulkBookingStatusFromModal(<?= $booking['id'] ?>, 6)">
                <i class="fas fa-times"></i> Cancel Request
            </button>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function updateBulkBookingStatusFromModal(bookingId, status) {
    var statusText = {
        1: 'mark as under review',
        4: 'mark as in progress',
        5: 'mark as completed',
        6: 'cancel'
    };
    
    var confirmMessage = "Are you sure you want to " + statusText[status] + " this bulk booking request?";
    
    if (confirm(confirmMessage)) {
        $.ajax({
            url: 'ajax/update_bulk_booking_status.php',
            type: 'POST',
            data: { 
                booking_id: bookingId,
                status: status
            },
            success: function(response) {
                if (response == 1) {
                    $('#bulkBookingDetailsModal').modal('hide');
                    location.reload();
                } else {
                    alert('Error updating booking status');
                }
            },
            error: function() {
                alert('Error updating booking status');
            }
        });
    }
}

function provideQuoteFromModal(bookingId) {
    $('#bulkBookingDetailsModal').modal('hide');
    setTimeout(function() {
        provideQuote(bookingId);
    }, 500);
}
</script>
