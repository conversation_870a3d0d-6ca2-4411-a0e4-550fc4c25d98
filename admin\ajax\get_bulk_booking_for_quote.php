<?php
session_start();
include('../lib/db_connection.php');
include('../lib/auth.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['booking_id'])) {
    echo json_encode(['error' => 'Invalid request']);
    exit;
}

$booking_id = intval($_POST['booking_id']);

$query = dbQuery("SELECT required_workers, total_budget, budget_per_worker 
                  FROM tabl_bulk_booking 
                  WHERE id = '$booking_id'");

if (dbNumRows($query) == 0) {
    echo json_encode(['error' => 'Booking not found']);
    exit;
}

$booking = dbFetchAssoc($query);
echo json_encode($booking);
?>
