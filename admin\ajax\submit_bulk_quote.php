<?php
session_start();
include('../lib/db_connection.php');
include('../lib/auth.php');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo '0';
    exit;
}

$booking_id = intval($_POST['booking_id'] ?? 0);
$quoted_amount = floatval($_POST['quoted_amount'] ?? 0);
$quoted_workers = intval($_POST['quoted_workers'] ?? 0);
$quote_details = mysqli_real_escape_string(dbConnection(), $_POST['quote_details'] ?? '');
$admin_notes = mysqli_real_escape_string(dbConnection(), $_POST['admin_notes'] ?? '');

// Validate required fields
if ($booking_id <= 0 || $quoted_amount <= 0 || $quoted_workers <= 0 || empty($quote_details)) {
    echo '0';
    exit;
}

// Check if booking exists
$checkQuery = dbQuery("SELECT id FROM tabl_bulk_booking WHERE id = '$booking_id'");
if (dbNumRows($checkQuery) == 0) {
    echo '0';
    exit;
}

// Update booking with quote
$updateFields = [
    "quoted_amount = '$quoted_amount'",
    "quoted_workers = '$quoted_workers'",
    "quote_details = '$quote_details'",
    "quote_date = NOW()",
    "status = 2", // Quote Provided
    "updated_at = NOW()"
];

if (!empty($admin_notes)) {
    $updateFields[] = "admin_notes = '$admin_notes'";
}

$updateQuery = "UPDATE tabl_bulk_booking SET " . implode(', ', $updateFields) . " WHERE id = '$booking_id'";

if (dbQuery($updateQuery)) {
    echo '1';
} else {
    echo '0';
}
?>
