<?php
session_start();
include('../lib/db_connection.php');
include('../lib/auth.php');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['booking_id']) || !isset($_POST['status'])) {
    echo '0';
    exit;
}

$booking_id = intval($_POST['booking_id']);
$status = intval($_POST['status']);

// Validate status
if (!in_array($status, [0, 1, 2, 3])) {
    echo '0';
    exit;
}

// Check if booking exists
$checkQuery = dbQuery("SELECT id, status FROM tabl_booking WHERE id = '$booking_id'");
if (dbNumRows($checkQuery) == 0) {
    echo '0';
    exit;
}

$currentBooking = dbFetchAssoc($checkQuery);

// Prepare update query based on status
$updateFields = ["status = '$status'", "updated_at = NOW()"];

// Add completion timestamp if marking as completed
if ($status == 2) {
    $updateFields[] = "completed_at = NOW()";
}

$updateQuery = "UPDATE tabl_booking SET " . implode(', ', $updateFields) . " WHERE id = '$booking_id'";

if (dbQuery($updateQuery)) {
    echo '1';
} else {
    echo '0';
}
?>
