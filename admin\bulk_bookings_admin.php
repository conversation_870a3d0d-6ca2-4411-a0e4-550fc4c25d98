<?php
session_start();
error_reporting(0);
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
include ('inc/function.php');

$page = 7;
$sub_page = 2;

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Bulk Booking Requests
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">

    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">
</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="javascript:void(0);">Bookings Management</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Bulk Booking Requests</a></li>
                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">

                                    <div class="widget-content widget-content-area br-6">
                                        <h4>Bulk Booking Requests Management</h4>
                                        <p class="text-muted">Manage bulk booking requests and provide quotes to customers</p>
                                        
                                        <div class="table-responsive mb-4 mt-4">
                                            <table id="zero-config" class="table table-hover" style="width:100%">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>Customer</th>
                                                        <th>Project Title</th>
                                                        <th>Category</th>
                                                        <th>Workers Needed</th>
                                                        <th>Budget</th>
                                                        <th>Urgency</th>
                                                        <th>Status</th>
                                                        <th>Created</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    $query = dbQuery("SELECT bb.*, 
                                                                             u.fullname as customer_name, u.phone_number as customer_phone,
                                                                             c.cat_name
                                                                      FROM tabl_bulk_booking bb
                                                                      LEFT JOIN tabl_users u ON bb.user_id = u.user_id
                                                                      LEFT JOIN tabl_categories c ON bb.cat_id = c.cat_id
                                                                      ORDER BY bb.created_at DESC");

                                                    if (dbNumRows($query) > 0) {
                                                        $s = 1;
                                                        while ($row = dbFetchAssoc($query)) {
                                                            $statusLabels = [
                                                                0 => '<span class="badge badge-warning">Pending Review</span>',
                                                                1 => '<span class="badge badge-info">Under Review</span>',
                                                                2 => '<span class="badge badge-primary">Quote Provided</span>',
                                                                3 => '<span class="badge badge-success">Quote Accepted</span>',
                                                                4 => '<span class="badge badge-secondary">In Progress</span>',
                                                                5 => '<span class="badge badge-success">Completed</span>',
                                                                6 => '<span class="badge badge-danger">Cancelled</span>'
                                                            ];
                                                            
                                                            $urgencyColors = [
                                                                'low' => 'text-success',
                                                                'medium' => 'text-warning',
                                                                'high' => 'text-danger',
                                                                'urgent' => 'text-danger font-weight-bold'
                                                            ];
                                                    ?>
                                                            <tr>
                                                                <td><?= $row['id'] ?></td>
                                                                <td>
                                                                    <strong><?= $row['customer_name'] ?? 'N/A' ?></strong><br>
                                                                    <small class="text-muted"><?= $row['customer_phone'] ?? 'N/A' ?></small>
                                                                </td>
                                                                <td>
                                                                    <strong><?= htmlspecialchars(substr($row['work_title'], 0, 30)) ?><?= strlen($row['work_title']) > 30 ? '...' : '' ?></strong><br>
                                                                    <small class="text-muted"><?= htmlspecialchars(substr($row['work_description'], 0, 50)) ?><?= strlen($row['work_description']) > 50 ? '...' : '' ?></small>
                                                                </td>
                                                                <td><?= $row['cat_name'] ?? 'N/A' ?></td>
                                                                <td class="text-center">
                                                                    <span class="badge badge-light"><?= $row['required_workers'] ?></span>
                                                                </td>
                                                                <td>
                                                                    <?php if ($row['total_budget']): ?>
                                                                        ₹<?= number_format($row['total_budget'], 0) ?>
                                                                    <?php elseif ($row['budget_per_worker']): ?>
                                                                        ₹<?= number_format($row['budget_per_worker'], 0) ?>/worker
                                                                    <?php else: ?>
                                                                        <span class="text-muted">Not specified</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td>
                                                                    <span class="<?= $urgencyColors[$row['urgency_level']] ?>">
                                                                        <?= ucfirst($row['urgency_level']) ?>
                                                                    </span>
                                                                </td>
                                                                <td><?= $statusLabels[$row['status']] ?></td>
                                                                <td>
                                                                    <?= date('d/m/Y', strtotime($row['created_at'])) ?><br>
                                                                    <small class="text-muted"><?= date('h:i A', strtotime($row['created_at'])) ?></small>
                                                                </td>
                                                                <td>
                                                                    <div class="btn-group" role="group">
                                                                        <button type="button" class="btn btn-sm btn-info" 
                                                                                onclick="viewBulkBookingDetails(<?= $row['id'] ?>)" 
                                                                                title="View Details">
                                                                            <i class="fas fa-eye"></i>
                                                                        </button>
                                                                        
                                                                        <?php if ($row['status'] == 0): ?>
                                                                        <button type="button" class="btn btn-sm btn-warning" 
                                                                                onclick="updateBulkBookingStatus(<?= $row['id'] ?>, 1)" 
                                                                                title="Mark Under Review">
                                                                            <i class="fas fa-search"></i>
                                                                        </button>
                                                                        <?php endif; ?>
                                                                        
                                                                        <?php if ($row['status'] <= 1): ?>
                                                                        <button type="button" class="btn btn-sm btn-primary" 
                                                                                onclick="provideQuote(<?= $row['id'] ?>)" 
                                                                                title="Provide Quote">
                                                                            <i class="fas fa-file-invoice-dollar"></i>
                                                                        </button>
                                                                        <?php endif; ?>
                                                                        
                                                                        <?php if ($row['status'] == 3): ?>
                                                                        <button type="button" class="btn btn-sm btn-secondary" 
                                                                                onclick="updateBulkBookingStatus(<?= $row['id'] ?>, 4)" 
                                                                                title="Mark In Progress">
                                                                            <i class="fas fa-play"></i>
                                                                        </button>
                                                                        <?php endif; ?>
                                                                        
                                                                        <?php if ($row['status'] == 4): ?>
                                                                        <button type="button" class="btn btn-sm btn-success" 
                                                                                onclick="updateBulkBookingStatus(<?= $row['id'] ?>, 5)" 
                                                                                title="Mark Completed">
                                                                            <i class="fas fa-check"></i>
                                                                        </button>
                                                                        <?php endif; ?>
                                                                        
                                                                        <?php if ($row['status'] != 6 && $row['status'] != 5): ?>
                                                                        <button type="button" class="btn btn-sm btn-danger" 
                                                                                onclick="updateBulkBookingStatus(<?= $row['id'] ?>, 6)" 
                                                                                title="Cancel Request">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                    <?php
                                                            $s++;
                                                        }
                                                    } else {
                                                        echo '<tr><td colspan="10" class="text-center">No bulk booking requests found</td></tr>';
                                                    }
                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- Bulk Booking Details Modal -->
    <div class="modal fade" id="bulkBookingDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bulkBookingDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkBookingDetailsModalLabel">Bulk Booking Request Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="bulkBookingDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Quote Modal -->
    <div class="modal fade" id="quoteModal" tabindex="-1" role="dialog" aria-labelledby="quoteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quoteModalLabel">Provide Quote</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="quoteForm">
                        <input type="hidden" id="quote_booking_id" name="booking_id">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="quoted_amount">Quoted Amount (₹) *</label>
                                    <input type="number" class="form-control" id="quoted_amount" name="quoted_amount"
                                           min="0" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="quoted_workers">Number of Workers *</label>
                                    <input type="number" class="form-control" id="quoted_workers" name="quoted_workers"
                                           min="1" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="quote_details">Quote Details *</label>
                            <textarea class="form-control" id="quote_details" name="quote_details" rows="5"
                                      placeholder="Provide detailed breakdown of the quote, timeline, and terms..." required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="admin_notes">Admin Notes (Internal)</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                      placeholder="Internal notes for admin reference..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitQuote()">Submit Quote</button>
                </div>
            </div>
        </div>
    </div>

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="plugins/table/datatable/datatables.js"></script>
    <script>
        $('#zero-config').DataTable({
            "oLanguage": {
                "oPaginate": {
                    "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                    "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                "sSearchPlaceholder": "Search...",
                "sLengthMenu": "Results :  _MENU_",
            },
            "stripeClasses": [],
            "lengthMenu": [10, 25, 50, 100],
            "pageLength": 25,
            "order": [[0, "desc"]]
        });
    </script>
    <!-- END PAGE LEVEL SCRIPTS -->

    <script>
        function viewBulkBookingDetails(bookingId) {
            $.ajax({
                url: 'ajax/get_bulk_booking_details.php',
                type: 'POST',
                data: { booking_id: bookingId },
                success: function(response) {
                    $('#bulkBookingDetailsContent').html(response);
                    $('#bulkBookingDetailsModal').modal('show');
                },
                error: function() {
                    alert('Error loading booking details');
                }
            });
        }

        function updateBulkBookingStatus(bookingId, status) {
            var statusText = {
                1: 'mark as under review',
                4: 'mark as in progress',
                5: 'mark as completed',
                6: 'cancel'
            };

            var confirmMessage = "Are you sure you want to " + statusText[status] + " this bulk booking request?";

            if (confirm(confirmMessage)) {
                $.ajax({
                    url: 'ajax/update_bulk_booking_status.php',
                    type: 'POST',
                    data: {
                        booking_id: bookingId,
                        status: status
                    },
                    success: function(response) {
                        if (response == 1) {
                            location.reload();
                        } else {
                            alert('Error updating booking status');
                        }
                    },
                    error: function() {
                        alert('Error updating booking status');
                    }
                });
            }
        }

        function provideQuote(bookingId) {
            // Load booking details to pre-fill form
            $.ajax({
                url: 'ajax/get_bulk_booking_for_quote.php',
                type: 'POST',
                data: { booking_id: bookingId },
                dataType: 'json',
                success: function(data) {
                    $('#quote_booking_id').val(bookingId);
                    $('#quoted_workers').val(data.required_workers);
                    if (data.total_budget) {
                        $('#quoted_amount').val(data.total_budget);
                    }
                    $('#quoteModal').modal('show');
                },
                error: function() {
                    alert('Error loading booking data');
                }
            });
        }

        function submitQuote() {
            var form = $('#quoteForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            $.ajax({
                url: 'ajax/submit_bulk_quote.php',
                type: 'POST',
                data: $('#quoteForm').serialize(),
                success: function(response) {
                    if (response == 1) {
                        $('#quoteModal').modal('hide');
                        location.reload();
                    } else {
                        alert('Error submitting quote');
                    }
                },
                error: function() {
                    alert('Error submitting quote');
                }
            });
        }
    </script>

</body>

</html>
