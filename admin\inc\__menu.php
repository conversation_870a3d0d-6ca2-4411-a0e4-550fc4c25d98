<div class="topbar-nav header navbar" role="banner">
    <nav id="topbar">
        <ul class="navbar-nav theme-brand flex-row  text-center">
            <li class="nav-item theme-text">
                <a href="home.php" class="nav-link">
                    <?php echo SITE; ?>
                </a>
            </li>
        </ul>

        <ul class="list-unstyled menu-categories" id="topAccordion">

            <li class="menu single-menu <?php if ($page == 1) {
                echo 'active';
            } else {
                echo '';
            } ?>">
                <a href="home.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-home">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>
                        <span>Home</span>
                    </div>

                </a>
            </li>

            <!-- Users -->
            <li class="menu single-menu 
                <?php if ($page == 2) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Users</span>
                    </div>

                </a>
                <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 1) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>">
                        <a href="users.php">Customers</a>
                    </li>
                    <li class="<?php if ($sub_page == 2) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>">
                        <a href="workers.php">workers </a>
                    </li>
                </ul>
            </li>
            <!-- Categories -->
            <li class="menu single-menu 
                <?php if ($page == 3) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="categories.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Categories</span>
                    </div>
                </a>
            </li>
            <!-- catalog -->
            <li class="menu single-menu 
                <?php if ($page == 4) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="worker_category.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Worker Profile Request</span>
                    </div>

                </a>
                <!-- <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 3) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>">
                        <a href="worker_category.php">worker categories</a>
                    </li>                 
                </ul> -->
            </li>

            <li class="menu single-menu 
                <?php if ($page == 5) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="services.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Services</span>
                    </div>
                </a>
            </li>
            <li class="menu single-menu 
                <?php if ($page == 6) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="worker_services.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Worker Services Request</span>
                    </div>
                </a>
            </li>
            <!-- Bookings Management -->
            <li class="menu single-menu
                <?php if ($page == 7) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-calendar">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        <span>Bookings Management</span>
                    </div>
                </a>
                <ul class="collapse submenu list-unstyled" id="bookings" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 1) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>">
                        <a href="worker_bookings.php">Worker Bookings</a>
                    </li>
                    <li class="<?php if ($sub_page == 2) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>">
                        <a href="bulk_bookings_admin.php">Bulk Booking Requests</a>
                    </li>
                    <li class="<?php if ($sub_page == 3) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>">
                        <a href="services_bookings.php">Service Bookings (Legacy)</a>
                    </li>
                </ul>
            </li>
        </ul>
    </nav>
</div>