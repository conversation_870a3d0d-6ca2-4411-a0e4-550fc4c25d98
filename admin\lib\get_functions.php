<?php
function get_all_particular(){
	$sel="SELECT * FROM tabl_particular ORDER BY par_name ASC";
	$qry=dbQuery($sel);
echo '<option value=" ">SELECT</option>';	
	while($res=dbFetchAssoc($qry)){
		echo '<option value="'.$res['id'].'" >'.$res['par_name'].'</option>';
	}
}

##########################################################################
function get_particular_name($id){
 $sel=dbQuery("SELECT par_name FROM tabl_particular WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['par_name']);
}

##########################################################################

function get_particular_image($id){
 $sel=dbQuery("SELECT par_image FROM tabl_particular WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return $res['par_image'];
}

##########################################################################

function get_all_color(){
	$sel="SELECT * FROM tabl_product_variation_value WHERE v_id=1 ORDER BY v_value ASC";
	$qry=dbQuery($sel);
echo '<option value="">SELECT</option>';	
	while($res=dbFetchAssoc($qry)){
		echo '<option value="'.$res['id'].'" >'.$res['v_value'].'</option>';
	}
}

##########################################################################

function get_all_size(){
	$sel="SELECT * FROM tabl_product_variation_value WHERE v_id=2 ORDER BY v_value ASC";
	$qry=dbQuery($sel);
echo '<option value="">SELECT</option>';	
	while($res=dbFetchAssoc($qry)){
		echo '<option value="'.$res['id'].'" >'.$res['v_value'].'</option>';
	}
}
##########################################################################
function get_course_name($id){
	
 $sel=dbQuery("SELECT name FROM tabl_courses WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['name']);
}
##########################################################################
function get_shop_name($id){
	
 $sel=dbQuery("SELECT name FROM tabl_shop_category WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['name']);
}

##########################################################################

function get_customer_name($id){
 $sel=dbQuery("SELECT name FROM tabl_user WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['name']);
}

##########################################################################

function get_customer_phone($id){
 $sel=dbQuery("SELECT mobile FROM tabl_user WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['mobile']);
}


##########################################################################

function get_customer_area($id){
 $sel=dbQuery("SELECT tabl_area.area as AREA,tabl_area.id,tabl_user.id FROM tabl_area INNER JOIN tabl_user ON tabl_user.area=tabl_area.id WHERE tabl_user.id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['AREA']);
}

##########################################################################

function get_area_name($id){
 $sel=dbQuery("SELECT area FROM tabl_area WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['area']);
}

##########################################################################

function get_creditors_name($id){
 $sel=dbQuery("SELECT name FROM tabl_creditors WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['name']);
}

##########################################################################

function get_creditors_phone($id){
 $sel=dbQuery("SELECT phone FROM tabl_creditors WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['phone']);
}
##########################################################################

function get_creditors_company($id){
 $sel=dbQuery("SELECT company_name FROM tabl_creditors WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['company_name']);
}

##########################################################################

function get_category_name($id){
 $sel=dbQuery("SELECT category FROM tabl_category WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['category']);
}

##########################################################################

function get_engineer_name($id){
 $sel=dbQuery("SELECT name FROM tabl_engineer WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['name']);
}

##########################################################################
function get_ce_engineer_name($id){
 $sel=dbQuery("SELECT name FROM tabl_chip_level_engineer WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['name']);
}
##########################################################################
function get_staff_name($id){
 $sel=dbQuery("SELECT name FROM tabl_staff WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['name']);
}



##########################################################################

function get_device_name($id){
 $sel=dbQuery("SELECT name FROM tabl_device WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['name']);
}

##########################################################################

function get_stuff_name($id){
 $sel=dbQuery("SELECT name FROM tabl_other_stuff WHERE id='".$id."'");
 $res=dbFetchAssoc($sel);
	return ucfirst($res['name']);
}

####################################################################

function total_category_paticular($cat_id){
 $stock=0;	
$sel=dbQuery("SELECT tabl_stock_master.*,tabl_stock.* FROM tabl_stock_master INNER JOIN tabl_stock ON tabl_stock_master.particular_id=tabl_stock.id WHERE tabl_stock.category_id='".$cat_id."'");
 while($res=dbFetchAssoc($sel)){ 
	 $stock+=$res['in_stock']-$res['out_stock'];
       }
	   return $stock;
	}
###################################################################	
	
function total_category_outstock_paticular($cat_id){
 $out_stock=0;	
$sel=dbQuery("SELECT tabl_stock_master.*,tabl_stock.* FROM tabl_stock_master INNER JOIN tabl_stock ON tabl_stock_master.particular_id=tabl_stock.id WHERE (tabl_stock_master.in_stock-tabl_stock_master.out_stock<=0) AND tabl_stock.category_id='".$cat_id."'");
 while($res=dbFetchAssoc($sel)){ 
	 $out_stock+=$res['in_stock']-$res['out_stock'];
       }
	   return $out_stock;
	}	
	
###################################################################


function total_category_dead_stock_paticular($cat_id){
$sel=dbQuery("SELECT tabl_dead_stock_details.particular_id,tabl_stock.*  FROM tabl_dead_stock_details INNER JOIN tabl_stock ON tabl_dead_stock_details.particular_id=tabl_stock.id WHERE tabl_stock.category_id='".$cat_id."'");
$num=dbNumRows($sel);
  return $num;
  	}	
	
function total_outstanding(){

  $sel=dbQuery("SELECT sum(receive_amount) as TtlAmt FROM tabl_cashbook WHERE type=1");
	$res=dbFetchAssoc($sel);
	if($res['TtlAmt']==NULL){
		$receive=0.00; 
		}else{
			$receive=$res['TtlAmt'];
			}
 

  $sel1=dbQuery("SELECT sum(payment_amount) as TtlAmt FROM tabl_cashbook WHERE type=2");
	$res1=dbFetchAssoc($sel1);
	  	if($res1['TtlAmt']==NULL){
		$payment=0.00; 
		}else{
			$payment=$res1['TtlAmt'];
			}
			
	return $receive-$payment;		
	
}
####################################################################
function total_outstanding_bankbook(){

  $sel=dbQuery("SELECT sum(receive_amount) as TtlAmt FROM tabl_bankbook WHERE type=1");
	$res=dbFetchAssoc($sel);
	if($res['TtlAmt']==NULL){
		$receive=0.00; 
		}else{
			$receive=$res['TtlAmt'];
			}
 

  $sel1=dbQuery("SELECT sum(payment_amount) as TtlAmt FROM tabl_bankbook WHERE type=2");
	$res1=dbFetchAssoc($sel1);
	  	if($res1['TtlAmt']==NULL){
		$payment=0.00; 
		}else{
			$payment=$res1['TtlAmt'];
			}
			
	return $receive-$payment;		
	
}

#########################################################################

function get_customer_outstanding($id){
$total_out=0;
$sel=dbQuery("SELECT sum(receive_amount) as TtlRec, sum(payment_amount) as TtlPay FROM tabl_main_ledger WHERE user_id='".$id."' AND (type_id=1 OR type_id=11)");
	$res=dbFetchAssoc($sel);
$total_out=$res['TtlRec']-$res['TtlPay'];
return $total_out;
}
#########################################################################

function get_customer_old_outstanding($id){
$total_out=0;
$sel=dbQuery("SELECT sum(receive_amount) as TtlRec, sum(payment_amount) as TtlPay FROM tabl_old_main_ledger WHERE user_id='".$id."' AND (type_id=1 OR type_id=11)");
	$res=dbFetchAssoc($sel);
$total_out=$res['TtlRec']-$res['TtlPay'];
return $total_out;
}
#########################################################################

function get_creditors_outstanding($id){
$total_out=0;
$sel=dbQuery("SELECT sum(receive_amount) as TtlRec, sum(payment_amount) as TtlPay FROM tabl_main_ledger WHERE creditors_id='".$id."'");
	$res=dbFetchAssoc($sel);
$total_out=$res['TtlRec']-$res['TtlPay'];
return $total_out;
}
#########################################################################

function get_creditors_old_outstanding($id){
$total_out=0;
$sel=dbQuery("SELECT sum(receive_amount) as TtlRec, sum(payment_amount) as TtlPay FROM tabl_old_main_ledger WHERE creditors_id='".$id."'");
	$res=dbFetchAssoc($sel);
$total_out=$res['TtlRec']-$res['TtlPay'];
return $total_out;
}
#########################################################################

function get_engineer_outstanding($id){
$total_out=0;
$sel=dbQuery("SELECT sum(receive_amount) as TtlRec, sum(payment_amount) as TtlPay FROM tabl_main_ledger WHERE engineer_id='".$id."'");
	$res=dbFetchAssoc($sel);
$total_out=$res['TtlRec']-$res['TtlPay'];
return $total_out;
}
#########################################################################

function get_ce_engineer_outstanding($id){
$total_out=0;
$sel=dbQuery("SELECT sum(receive_amount) as TtlRec, sum(payment_amount) as TtlPay FROM tabl_main_ledger WHERE chip_level_engineer='".$id."' AND user_id=0");
	$res=dbFetchAssoc($sel);
$total_out=$res['TtlRec']-$res['TtlPay'];
return $total_out;
}

#########################################################################

function get_ce_engineer_old_outstanding($id){
$total_out=0;
$sel=dbQuery("SELECT sum(receive_amount) as TtlRec, sum(payment_amount) as TtlPay FROM tabl_old_main_ledger WHERE chip_level_engineer='".$id."' AND user_id=0");
	$res=dbFetchAssoc($sel);
$total_out=$res['TtlRec']-$res['TtlPay'];
return $total_out;
}

#########################################################################
function get_available_stock($id){
$sel=dbQuery("SELECT * FROM tabl_stock_master WHERE particular_id='".$id."'");
$res=dbFetchAssoc($sel);
$total_stock=$res['in_stock']-$res['out_stock'];
return $total_stock;
}
function get_available_stock_price($id){
$sel=dbQuery("SELECT sum(total_sale_price) as TtlSale,sum(total_purchase_price) as TtlPurchase FROM tabl_stock_ledger_details WHERE particular_id='".$id."'");
$res=dbFetchAssoc($sel);
$total_stock_price=$res['TtlSale']-$res['TtlPurchase'];
return $total_stock_price;
}
#########################################################################
function get_engineer_commission($id){
  $sel=dbQuery("SELECT staff,engineer_id,sum(receive_amount) as TtlRec,sum(payment_amount) as TtlPay FROM tabl_main_ledger WHERE is_commission=1 AND engineer_id='".$id."'");
	$res=dbFetchAssoc($sel);
$outstanding=$res['TtlRec']-$res['TtlPay'];
return $outstanding;
}
#########################################################################
function get_staff_commission($id){
  $sel=dbQuery("SELECT staff,engineer_id,sum(receive_amount) as TtlRec,sum(payment_amount) as TtlPay FROM tabl_main_ledger WHERE is_commission=1 AND staff='".$id."'");
	$res=dbFetchAssoc($sel);
$outstanding=$res['TtlRec']-$res['TtlPay'];
return $outstanding;
}
#########################################################################
function get_sale_datewise($date){
  
  $sel=dbQuery("SELECT tabl_sale.date_added,tabl_sale_details.particular_id,sum(tabl_sale_details.qty) as Qty,sum(tabl_sale_details.size) as Inr FROM tabl_sale INNER JOIN tabl_sale_details ON tabl_sale.id=tabl_sale_details.sale_id WHERE DATE(tabl_sale.date_added) = '".$date."'");
	$res=dbFetchAssoc($sel);

return $res['Qty'];
}

#########################################################################

function get_monthly_collection($user_id,$admin_type,$month){
	if($admin_type==3){
	$sel=dbQuery("SELECT sum(payment_amount) as TtlCol FROM `tabl_main_ledger` WHERE `engineer_id`='".$user_id."' AND (type=1 OR type=4) AND MONTH(date_added) ='".$month."'");
	}else{
		$sel=dbQuery("SELECT sum(payment_amount) as TtlCol FROM `tabl_main_ledger` WHERE `staff`='".$user_id."' AND (type=1 OR type=4) AND MONTH(date_added) ='".$month."'");
	}
	$res=dbFetchAssoc($sel);
	echo $res['TtlCol'];
}

######################################################################

function get_monthly_commission($user_id,$admin_type,$month){
	if($admin_type==3){
	$sel=dbQuery("SELECT sum(receive_amount) as Ttlrec FROM `tabl_main_ledger` WHERE `engineer_id`='".$user_id."' AND is_commission=1 AND type=4 AND MONTH(date_added) ='".$month."'");
	}else{
	$sel=dbQuery("SELECT sum(receive_amount) as Ttlrec FROM `tabl_main_ledger` WHERE `staff`='".$user_id."' AND is_commission=1 AND type=4 AND MONTH(date_added) ='".$month."'");
	}
	$res=dbFetchAssoc($sel);
	echo $res['Ttlrec'];
}
######################################################################
function get_month_name($month){
  if($month==1){
	  echo 'January';
  }elseif($month==2){
	  echo 'Febuary';
  }elseif($month==3){
	  echo 'March';
  }elseif($month==4){
	  echo 'April';
  }elseif($month==5){
	  echo 'May';
  }elseif($month==6){
	  echo 'June';
  }elseif($month==7){
	  echo 'July';
  }elseif($month==8){
	  echo 'August';
  }elseif($month==9){
	  echo 'September';
  }elseif($month==10){
	  echo 'October';
  }elseif($month==11){
	  echo 'November';
  }else{
	  echo 'December';
  }

}
#####################################################################

function get_problem($ticket){
	 $sel=dbQuery("SELECT * FROM `tabl_call_repair` WHERE id='".$ticket."'");
     $res=dbFetchAssoc($sel);
	return $res['problem'];
}
