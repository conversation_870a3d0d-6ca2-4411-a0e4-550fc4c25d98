<?php
session_start();
error_reporting(0);
include ('lib/db_connection.php');
include ('lib/get_functions.php');
include ('lib/auth.php');
include ('inc/resize-class.php');
include ('inc/function.php');

$page = 7;
$sub_page = 1;

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>
        <?php echo SITE; ?> - Worker Bookings
    </title>
    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/datatables.css">
    <link rel="stylesheet" type="text/css" href="plugins/table/datatable/dt-global_style.css">

    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/regular.css">
    <link rel="stylesheet" href="plugins/font-icons/fontawesome/css/fontawesome.css">
</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include ('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include ('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="javascript:void(0);">Bookings Management</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Worker Bookings</a></li>
                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll"
                            data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-sm-12  layout-spacing">

                                    <div class="widget-content widget-content-area br-6">
                                        <h4>Worker Bookings Management</h4>
                                        <p class="text-muted">Manage individual worker bookings from the new booking system</p>
                                        
                                        <div class="table-responsive mb-4 mt-4">
                                            <table id="zero-config" class="table table-hover" style="width:100%">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>Customer</th>
                                                        <th>Worker</th>
                                                        <th>Category</th>
                                                        <th>Booking Date</th>
                                                        <th>Total Price</th>
                                                        <th>Status</th>
                                                        <th>Created</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    $query = dbQuery("SELECT b.*, 
                                                                             u.fullname as customer_name, u.phone_number as customer_phone,
                                                                             w.name as worker_name, w.phone as worker_phone,
                                                                             c.cat_name
                                                                      FROM tabl_booking b
                                                                      LEFT JOIN tabl_users u ON b.user_id = u.user_id
                                                                      LEFT JOIN tabl_workers w ON b.worker_id = w.worker_id
                                                                      LEFT JOIN tabl_categories c ON b.cat_id = c.cat_id
                                                                      ORDER BY b.created_at DESC");

                                                    if (dbNumRows($query) > 0) {
                                                        $s = 1;
                                                        while ($row = dbFetchAssoc($query)) {
                                                            $statusLabels = [
                                                                0 => '<span class="badge badge-warning">Pending</span>',
                                                                1 => '<span class="badge badge-info">Confirmed</span>',
                                                                2 => '<span class="badge badge-success">Completed</span>',
                                                                3 => '<span class="badge badge-danger">Cancelled</span>'
                                                            ];
                                                    ?>
                                                            <tr>
                                                                <td><?= $row['id'] ?></td>
                                                                <td>
                                                                    <strong><?= $row['customer_name'] ?? 'N/A' ?></strong><br>
                                                                    <small class="text-muted"><?= $row['customer_phone'] ?? 'N/A' ?></small>
                                                                </td>
                                                                <td>
                                                                    <strong><?= $row['worker_name'] ?? 'N/A' ?></strong><br>
                                                                    <small class="text-muted"><?= $row['worker_phone'] ?? 'N/A' ?></small>
                                                                </td>
                                                                <td><?= $row['cat_name'] ?? 'N/A' ?></td>
                                                                <td>
                                                                    <?= date('d/m/Y', strtotime($row['booking_date'])) ?><br>
                                                                    <small class="text-muted"><?= date('h:i A', strtotime($row['booking_time'])) ?></small>
                                                                </td>
                                                                <td>
                                                                    <?php if ($row['total_price']): ?>
                                                                        ₹<?= number_format($row['total_price'], 2) ?>
                                                                    <?php else: ?>
                                                                        <span class="text-muted">Not set</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td><?= $statusLabels[$row['status']] ?></td>
                                                                <td>
                                                                    <?= date('d/m/Y h:i A', strtotime($row['created_at'])) ?>
                                                                </td>
                                                                <td>
                                                                    <div class="btn-group" role="group">
                                                                        <button type="button" class="btn btn-sm btn-info" 
                                                                                onclick="viewBookingDetails(<?= $row['id'] ?>)" 
                                                                                title="View Details">
                                                                            <i class="fas fa-eye"></i>
                                                                        </button>
                                                                        
                                                                        <?php if ($row['status'] == 0): ?>
                                                                        <button type="button" class="btn btn-sm btn-success" 
                                                                                onclick="updateBookingStatus(<?= $row['id'] ?>, 1)" 
                                                                                title="Confirm Booking">
                                                                            <i class="fas fa-check"></i>
                                                                        </button>
                                                                        <?php endif; ?>
                                                                        
                                                                        <?php if ($row['status'] == 1): ?>
                                                                        <button type="button" class="btn btn-sm btn-primary" 
                                                                                onclick="updateBookingStatus(<?= $row['id'] ?>, 2)" 
                                                                                title="Mark Completed">
                                                                            <i class="fas fa-star"></i>
                                                                        </button>
                                                                        <?php endif; ?>
                                                                        
                                                                        <?php if ($row['status'] != 3): ?>
                                                                        <button type="button" class="btn btn-sm btn-danger" 
                                                                                onclick="updateBookingStatus(<?= $row['id'] ?>, 3)" 
                                                                                title="Cancel Booking">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                    <?php
                                                            $s++;
                                                        }
                                                    } else {
                                                        echo '<tr><td colspan="9" class="text-center">No worker bookings found</td></tr>';
                                                    }
                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include ('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- Booking Details Modal -->
    <div class="modal fade" id="bookingDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookingDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bookingDetailsModalLabel">Booking Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="bookingDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        $(document).ready(function () {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="plugins/table/datatable/datatables.js"></script>
    <script>
        $('#zero-config').DataTable({
            "oLanguage": {
                "oPaginate": {
                    "sPrevious": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>',
                    "sNext": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>'
                },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>',
                "sSearchPlaceholder": "Search...",
                "sLengthMenu": "Results :  _MENU_",
            },
            "stripeClasses": [],
            "lengthMenu": [10, 25, 50, 100],
            "pageLength": 25,
            "order": [[0, "desc"]]
        });
    </script>
    <!-- END PAGE LEVEL SCRIPTS -->

    <script>
        function viewBookingDetails(bookingId) {
            $.ajax({
                url: 'ajax/get_booking_details.php',
                type: 'POST',
                data: { booking_id: bookingId },
                success: function(response) {
                    $('#bookingDetailsContent').html(response);
                    $('#bookingDetailsModal').modal('show');
                },
                error: function() {
                    alert('Error loading booking details');
                }
            });
        }

        function updateBookingStatus(bookingId, status) {
            var statusText = {
                1: 'confirm',
                2: 'mark as completed',
                3: 'cancel'
            };

            var confirmMessage = "Are you sure you want to " + statusText[status] + " this booking?";

            if (confirm(confirmMessage)) {
                $.ajax({
                    url: 'ajax/update_booking_status.php',
                    type: 'POST',
                    data: {
                        booking_id: bookingId,
                        status: status
                    },
                    success: function(response) {
                        if (response == 1) {
                            location.reload();
                        } else {
                            alert('Error updating booking status');
                        }
                    },
                    error: function() {
                        alert('Error updating booking status');
                    }
                });
            }
        }
    </script>

</body>

</html>
