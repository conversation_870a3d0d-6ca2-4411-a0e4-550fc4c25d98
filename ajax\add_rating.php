<?php
session_start();
require_once ('../admin/lib/db_connection.php');
header('Content-Type: application/json');

// Check if rating form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['rating']) && isset($_POST['comment'])) {
    $worker_category_id = $_POST['worker_id']; // This is actually worker_category_id from the URL
    $rating = intval($_POST['rating']);
    $comment = $_POST['comment'];
    $user_id = $_SESSION['user_id'];

    // Get actual worker_id from worker_category table
    $workerQuery = dbQuery("SELECT worker_id FROM tabl_workers_category WHERE id = '$worker_category_id'");
    $workerData = dbFetchAssoc($workerQuery);
    $actual_worker_id = $workerData['worker_id'];

    // Check if user has completed bookings with this worker
    $completedBookingsQuery = dbQuery("SELECT id FROM tabl_worker_bookings
                                      WHERE user_id = '$user_id'
                                      AND worker_category_id = '$worker_category_id'
                                      AND status = 2
                                      LIMIT 1");

    if(mysqli_num_rows($completedBookingsQuery) > 0) {
        $completedBooking = dbFetchAssoc($completedBookingsQuery);
        $booking_id = $completedBooking['id'];

        // Check if user has already rated this worker
        $existingRatingQuery = dbQuery("SELECT id FROM tabl_worker_ratings
                                      WHERE user_id = '$user_id'
                                      AND worker_id = '$actual_worker_id'");

        if(mysqli_num_rows($existingRatingQuery) == 0) {
            // Insert rating into database
            $sqlExt = dbQuery("INSERT INTO tabl_worker_ratings (worker_id, user_id, booking_id, rating, comment)
                             VALUES ('$actual_worker_id', '$user_id', '$booking_id', '$rating', '$comment')");

            if ($sqlExt) {
                echo json_encode(array("success" => true, "message" => "Rating added successfully"));
            } else {
                echo json_encode(array("success" => false, "message" => "Failed to add rating"));
            }
        } else {
            echo json_encode(array("success" => false, "message" => "You have already rated this worker"));
        }
    } else {
        echo json_encode(array("success" => false, "message" => "You can only rate workers after completing a service"));
    }
} else {
    echo json_encode(array("success" => false, "message" => "Invalid request"));
}
?>
