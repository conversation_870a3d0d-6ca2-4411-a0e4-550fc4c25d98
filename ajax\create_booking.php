<?php
require_once('../admin/lib/db_connection.php');
require_once('../lib/auth.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get user ID from session
$user_id = $_SESSION['user_id'] ?? null;
if (!$user_id) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Get POST data
$worker_category_id = $_POST['worker_category_id'] ?? '';
$booking_date = $_POST['booking_date'] ?? '';
$booking_time = $_POST['booking_time'] ?? '';
$service_description = $_POST['service_description'] ?? '';
$user_address = $_POST['user_address'] ?? '';
$user_phone = $_POST['user_phone'] ?? '';
$total_price = $_POST['total_price'] ?? '';

// Validate required fields
if (empty($worker_category_id) || empty($booking_date) || empty($booking_time) || empty($user_address) || empty($user_phone)) {
    echo json_encode(['success' => false, 'message' => 'Please fill all required fields']);
    exit;
}

// Get worker category details
$workerCategoryQuery = dbQuery("SELECT * FROM tabl_workers_category WHERE id = '$worker_category_id' AND status = 1");
if (dbNumRows($workerCategoryQuery) == 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid worker category']);
    exit;
}

$workerCategory = dbFetchAssoc($workerCategoryQuery);
$worker_id = $workerCategory['worker_id'];
$cat_id = $workerCategory['cat_id'];

// Calculate total price if not provided
if (empty($total_price) && !empty($workerCategory['price'])) {
    $total_price = $workerCategory['price'];
    
    // If hourly rate and minimum hours specified
    if ($workerCategory['price_type'] == 'hourly' && !empty($workerCategory['min_service_hours'])) {
        $total_price = $workerCategory['price'] * $workerCategory['min_service_hours'];
    }
}

// Validate booking date (should be future date)
$booking_datetime = $booking_date . ' ' . $booking_time;
if (strtotime($booking_datetime) <= time()) {
    echo json_encode(['success' => false, 'message' => 'Booking date and time must be in the future']);
    exit;
}

// Check if user already has a pending booking with this worker
$existingBookingQuery = dbQuery("SELECT id FROM tabl_booking 
                                WHERE user_id = '$user_id' 
                                AND worker_category_id = '$worker_category_id' 
                                AND status IN (0, 1) 
                                AND booking_date >= CURDATE()");

if (dbNumRows($existingBookingQuery) > 0) {
    echo json_encode(['success' => false, 'message' => 'You already have a pending booking with this worker']);
    exit;
}

// Insert booking
$insertQuery = "INSERT INTO tabl_booking (
    user_id, 
    worker_category_id, 
    worker_id, 
    cat_id, 
    booking_date, 
    booking_time, 
    service_description, 
    user_address, 
    user_phone, 
    total_price, 
    status, 
    created_at, 
    updated_at
) VALUES (
    '$user_id',
    '$worker_category_id',
    '$worker_id',
    '$cat_id',
    '$booking_date',
    '$booking_time',
    '$service_description',
    '$user_address',
    '$user_phone',
    '$total_price',
    0,
    NOW(),
    NOW()
)";

if (dbQuery($insertQuery)) {
    $booking_id = mysqli_insert_id(dbConnection());
    
    // Get worker details for notification (if needed)
    $workerQuery = dbQuery("SELECT name, phone FROM tabl_workers WHERE worker_id = '$worker_id'");
    $worker = dbFetchAssoc($workerQuery);
    
    echo json_encode([
        'success' => true, 
        'message' => 'Booking created successfully! The worker will contact you soon.',
        'booking_id' => $booking_id,
        'worker_name' => $worker['name'] ?? 'Worker'
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to create booking. Please try again.']);
}
?>
