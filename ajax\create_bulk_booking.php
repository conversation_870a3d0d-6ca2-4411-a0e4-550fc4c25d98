<?php
require_once('../admin/lib/db_connection.php');
require_once('../lib/auth.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get user ID from session
$user_id = $_SESSION['user_id'] ?? null;
if (!$user_id) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Get POST data and sanitize
$cat_id = mysqli_real_escape_string(dbConnection(), $_POST['cat_id'] ?? '');
$work_title = mysqli_real_escape_string(dbConnection(), $_POST['work_title'] ?? '');
$work_description = mysqli_real_escape_string(dbConnection(), $_POST['work_description'] ?? '');
$required_workers = intval($_POST['required_workers'] ?? 1);
$budget_per_worker = !empty($_POST['budget_per_worker']) ? floatval($_POST['budget_per_worker']) : null;
$total_budget = !empty($_POST['total_budget']) ? floatval($_POST['total_budget']) : null;
$required_experience = mysqli_real_escape_string(dbConnection(), $_POST['required_experience'] ?? '');
$work_duration = mysqli_real_escape_string(dbConnection(), $_POST['work_duration'] ?? '');
$start_date = !empty($_POST['start_date']) ? $_POST['start_date'] : null;
$end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
$location_type = mysqli_real_escape_string(dbConnection(), $_POST['location_type'] ?? 'single');
$primary_address = mysqli_real_escape_string(dbConnection(), $_POST['primary_address'] ?? '');
$additional_addresses = mysqli_real_escape_string(dbConnection(), $_POST['additional_addresses'] ?? '');
$city = mysqli_real_escape_string(dbConnection(), $_POST['city'] ?? '');
$state = mysqli_real_escape_string(dbConnection(), $_POST['state'] ?? '');
$pincode = mysqli_real_escape_string(dbConnection(), $_POST['pincode'] ?? '');
$contact_person = mysqli_real_escape_string(dbConnection(), $_POST['contact_person'] ?? '');
$contact_phone = mysqli_real_escape_string(dbConnection(), $_POST['contact_phone'] ?? '');
$contact_email = mysqli_real_escape_string(dbConnection(), $_POST['contact_email'] ?? '');
$alternative_phone = mysqli_real_escape_string(dbConnection(), $_POST['alternative_phone'] ?? '');
$preferred_contact_time = mysqli_real_escape_string(dbConnection(), $_POST['preferred_contact_time'] ?? '');
$urgency_level = mysqli_real_escape_string(dbConnection(), $_POST['urgency_level'] ?? 'medium');
$special_requirements = mysqli_real_escape_string(dbConnection(), $_POST['special_requirements'] ?? '');

// Validate required fields
if (empty($cat_id) || empty($work_title) || empty($work_description) || empty($primary_address) || 
    empty($city) || empty($state) || empty($pincode) || empty($contact_person) || empty($contact_phone)) {
    echo json_encode(['success' => false, 'message' => 'Please fill all required fields']);
    exit;
}

// Validate category exists
$catQuery = dbQuery("SELECT cat_name FROM tabl_categories WHERE cat_id = '$cat_id' AND cat_status = 1");
if (dbNumRows($catQuery) == 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid service category selected']);
    exit;
}

// Validate worker count
if ($required_workers < 1 || $required_workers > 100) {
    echo json_encode(['success' => false, 'message' => 'Number of workers must be between 1 and 100']);
    exit;
}

// Validate pincode
if (!preg_match('/^[0-9]{6}$/', $pincode)) {
    echo json_encode(['success' => false, 'message' => 'Please enter a valid 6-digit pincode']);
    exit;
}

// Validate phone number
if (!preg_match('/^[0-9]{10}$/', preg_replace('/[^0-9]/', '', $contact_phone))) {
    echo json_encode(['success' => false, 'message' => 'Please enter a valid 10-digit phone number']);
    exit;
}

// Validate dates if provided
if ($start_date && $end_date && strtotime($end_date) < strtotime($start_date)) {
    echo json_encode(['success' => false, 'message' => 'End date cannot be before start date']);
    exit;
}

// Check for duplicate requests (same user, category, and similar title within last 7 days)
$duplicateQuery = dbQuery("SELECT id FROM tabl_bulk_booking 
                          WHERE user_id = '$user_id' 
                          AND cat_id = '$cat_id' 
                          AND work_title = '$work_title'
                          AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                          AND status NOT IN (5, 6)"); // Not completed or cancelled

if (dbNumRows($duplicateQuery) > 0) {
    echo json_encode(['success' => false, 'message' => 'You have already submitted a similar request recently. Please check your bulk bookings.']);
    exit;
}

// Prepare SQL values
$start_date_sql = $start_date ? "'$start_date'" : 'NULL';
$end_date_sql = $end_date ? "'$end_date'" : 'NULL';
$budget_per_worker_sql = $budget_per_worker ? "'$budget_per_worker'" : 'NULL';
$total_budget_sql = $total_budget ? "'$total_budget'" : 'NULL';
$contact_email_sql = $contact_email ? "'$contact_email'" : 'NULL';
$alternative_phone_sql = $alternative_phone ? "'$alternative_phone'" : 'NULL';
$preferred_contact_time_sql = $preferred_contact_time ? "'$preferred_contact_time'" : 'NULL';
$required_experience_sql = $required_experience ? "'$required_experience'" : 'NULL';
$work_duration_sql = $work_duration ? "'$work_duration'" : 'NULL';
$additional_addresses_sql = $additional_addresses ? "'$additional_addresses'" : 'NULL';
$special_requirements_sql = $special_requirements ? "'$special_requirements'" : 'NULL';

// Insert bulk booking request
$insertQuery = "INSERT INTO tabl_bulk_booking (
    user_id, cat_id, work_title, work_description, required_workers, 
    budget_per_worker, total_budget, required_experience, work_duration, 
    start_date, end_date, location_type, primary_address, additional_addresses, 
    city, state, pincode, contact_person, contact_phone, contact_email, 
    alternative_phone, preferred_contact_time, urgency_level, special_requirements, 
    status, created_at, updated_at
) VALUES (
    '$user_id', '$cat_id', '$work_title', '$work_description', '$required_workers',
    $budget_per_worker_sql, $total_budget_sql, $required_experience_sql, $work_duration_sql,
    $start_date_sql, $end_date_sql, '$location_type', '$primary_address', $additional_addresses_sql,
    '$city', '$state', '$pincode', '$contact_person', '$contact_phone', $contact_email_sql,
    $alternative_phone_sql, $preferred_contact_time_sql, '$urgency_level', $special_requirements_sql,
    0, NOW(), NOW()
)";

if (dbQuery($insertQuery)) {
    $booking_id = mysqli_insert_id(dbConnection());
    
    // Get category name for response
    $categoryData = dbFetchAssoc($catQuery);
    
    echo json_encode([
        'success' => true, 
        'message' => 'Your bulk booking request has been submitted successfully! Our team will review and contact you within 24-48 hours.',
        'booking_id' => $booking_id,
        'category' => $categoryData['cat_name']
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to submit request. Please try again.']);
}
?>
