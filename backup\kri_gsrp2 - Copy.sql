-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: Jun 08, 2025 at 05:55 PM
-- Server version: 8.3.0
-- PHP Version: 8.3.6

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `kri_gsrp2`
--

-- --------------------------------------------------------

--
-- Table structure for table `tabl_admin`
--

DROP TABLE IF EXISTS `tabl_admin`;
CREATE TABLE IF NOT EXISTS `tabl_admin` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user` varchar(50) NOT NULL,
  `password` varchar(100) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(50) NOT NULL,
  `profile_image` text NOT NULL,
  `admin_type` int NOT NULL DEFAULT '0' COMMENT '1=Admin 2=Instructor',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

--
-- Dumping data for table `tabl_admin`
--

INSERT INTO `tabl_admin` (`id`, `user`, `password`, `name`, `email`, `profile_image`, `admin_type`) VALUES
(1, 'admin', 'admin@123', 'Super Admin', '<EMAIL>', '212970.jpeg', 1);

-- --------------------------------------------------------

--
-- Table structure for table `tabl_categories`
--

DROP TABLE IF EXISTS `tabl_categories`;
CREATE TABLE IF NOT EXISTS `tabl_categories` (
  `cat_id` int NOT NULL AUTO_INCREMENT,
  `cat_name` varchar(255) DEFAULT NULL,
  `cat_icon` varchar(255) DEFAULT NULL,
  `description` text NOT NULL,
  `created_at` date DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `cat_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1 = active, 0 = inactive',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`cat_id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_categories`
--

INSERT INTO `tabl_categories` (`cat_id`, `cat_name`, `cat_icon`, `description`, `created_at`, `updated_at`, `cat_status`, `is_deleted`) VALUES
(1, 'AC mechanic', '668438.jpg', 'Ac mechanic, Fridge mechanic', '2024-04-26', '0000-00-00 00:00:00', 1, 0),
(3, 'Painter', '748184.jpg', 'Painter are available', '2024-04-26', '0000-00-00 00:00:00', 1, 0),
(6, 'Plumber', '497933.jpeg', 'All kind of plumber are available\r\n', '2024-05-22', NULL, 1, 0),
(7, 'Electrician', '515530.jpeg', 'All kind of electrical workers are available', '2024-05-24', NULL, 1, 0),
(9, 'TV mechanic', '394260.jpg', 'TV Repair', '2024-05-25', NULL, 1, 0),
(10, 'Maid', '259541.jpg', 'Maids are available', '2024-05-25', NULL, 1, 0),
(11, 'Toilet Cleaner', '895418.jpg', 'Toilet and washroom and Bathroom cleaning services available', '2024-05-25', NULL, 1, 0),
(12, 'Drivers', '339861.jpg', 'All kinds of Driver are available', '2024-05-25', NULL, 1, 0),
(13, 'Men grooming', '330885.jpg', 'All kinds of men grooming ', '2024-05-25', NULL, 1, 0),
(14, 'Beautician', '869506.jpg', 'Women Beautician service are available', '2024-05-25', NULL, 1, 0),
(15, 'Mistri', '900804.jpg', 'Raj mistri are available', '2024-05-25', NULL, 1, 0),
(16, 'Civil Labor', '779323.jpg', 'Labor for site worky', '2024-05-25', NULL, 1, 0),
(17, 'Gardner', '148161.jpg', 'Gardener are available', '2024-05-25', NULL, 1, 0),
(18, 'Photographer', '939261.jpg', 'Photographer and videographer', '2024-05-25', NULL, 1, 0),
(20, 'Tailor', '33877.jpg', 'Book your tailor', '2024-05-25', NULL, 1, 0),
(21, 'Bike mech', '466930.jpg', 'Bike mechanic are available', '2024-05-25', NULL, 1, 0),
(22, 'RO mech', '533073.jpg', 'Ro mechanic are available', '2024-05-25', NULL, 1, 0),
(23, 'Pandit ji', '55043.jpg', 'Pandit ji, astrology', '2024-05-26', NULL, 1, 0),
(24, 'Washing machine', '266178.jpg', 'Washing machine mechanic', '2024-05-26', NULL, 1, 0),
(26, 'Carpenter', '370482.jpg', 'All kind of word work', '2024-06-03', NULL, 1, 0),
(27, 'Machine operator', '382069.jpg', 'Different kind of operator', '2024-06-04', NULL, 1, 0),
(28, 'Petrol pump ', '809948.jpg', 'Petrol pump worker', '2024-08-21', NULL, 1, 0),
(29, 'Bartenders', '984996.jpg', 'Bartenders ', '2024-08-21', NULL, 1, 0),
(30, 'Dishwashers ', '377293.jpg', 'Dishwashers ', '2024-08-21', NULL, 1, 0),
(31, 'Retail & Warehouse worker ', '606687.jpeg', 'Retail worker ', '2024-08-30', NULL, 1, 0),
(32, 'General labour ', '931331.jpeg', 'General labour ', '2024-08-30', NULL, 1, 0),
(33, 'Sewing worker ', '115060.jpg', 'Sewing ladies ', '2024-09-02', NULL, 1, 0),
(34, 'Hotel staff', '652623.jpg', 'Hotel staff ', '2024-10-04', NULL, 1, 0),
(35, 'Female staff', '180107.png', 'Female staff', '2024-10-06', NULL, 1, 0),
(36, 'Cook ', '898972.jpg', 'Cook', '2025-01-21', NULL, 1, 0);

-- --------------------------------------------------------

--
-- Table structure for table `tabl_countries`
--

DROP TABLE IF EXISTS `tabl_countries`;
CREATE TABLE IF NOT EXISTS `tabl_countries` (
  `country_id` int NOT NULL AUTO_INCREMENT,
  `country_name` varchar(100) NOT NULL,
  `country_code` varchar(5) DEFAULT NULL,
  `status` tinyint(1) DEFAULT '1' COMMENT '1 = active, 0 = inactive',
  `is_deleted` tinyint(1) DEFAULT '0',
  `date_added` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`country_id`),
  KEY `idx_country_name` (`country_name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_countries`
--

INSERT INTO `tabl_countries` (`country_id`, `country_name`, `country_code`, `status`, `is_deleted`, `date_added`, `date_updated`) VALUES
(1, 'India', 'IN', 1, 0, '2024-03-30 17:56:40', '2024-03-30 17:56:40'),
(2, 'United States', 'US', 1, 0, '2024-03-30 17:56:40', '2024-03-30 17:56:40'),
(3, 'United Kingdom', 'UK', 1, 0, '2024-03-30 17:56:40', '2024-03-30 17:56:40'),
(4, 'Canada', 'CA', 1, 0, '2024-03-30 17:56:40', '2024-03-30 17:56:40'),
(5, 'Australia', 'AU', 1, 0, '2024-03-30 17:56:40', '2024-03-30 17:56:40'),
(6, 'Germany', 'DE', 1, 0, '2024-03-30 17:56:40', '2024-03-30 17:56:40');

-- --------------------------------------------------------

--
-- Table structure for table `tabl_districts`
--

DROP TABLE IF EXISTS `tabl_districts`;
CREATE TABLE IF NOT EXISTS `tabl_districts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `district_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `state_id` int DEFAULT NULL,
  `status` enum('active','inactive') COLLATE utf8mb4_general_ci DEFAULT 'active',
  `is_deleted` tinyint(1) DEFAULT '0',
  `date_added` date NOT NULL,
  `date_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `state_id` (`state_id`),
  KEY `idx_district_name` (`district_name`)
) ENGINE=InnoDB AUTO_INCREMENT=744 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tabl_districts`
--

INSERT INTO `tabl_districts` (`id`, `district_name`, `image`, `state_id`, `status`, `is_deleted`, `date_added`, `date_updated`) VALUES
(1, 'South Andaman', '', 1, 'active', 0, '2024-03-30', '2024-03-30 18:20:52'),
(2, 'Nicobar', '', 1, 'active', 0, '2024-03-30', '2024-03-30 18:20:52'),
(3, 'North and Middle Andaman', '', 1, 'active', 0, '2024-03-30', '2024-03-30 18:20:52'),
(4, 'Anantapur', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(5, 'Chittoor', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(6, 'East Godavari', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(7, 'Guntur', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(8, 'Krishna', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(9, 'Kurnool', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(10, 'Prakasam', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(11, 'Sri Potti Sriramulu Nellore', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(12, 'Srikakulam', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(13, 'Visakhapatnam', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(14, 'Vizianagaram', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(15, 'West Godavari', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(16, 'YSR Kadapa (Cuddapah)', '', 2, 'active', 0, '2024-03-30', '2024-03-30 18:29:40'),
(17, 'Tawang', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(18, 'West Kameng', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(19, 'East Kameng', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(20, 'Papum Pare', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(21, 'Kurung Kumey', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(22, 'Kra Daadi', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(23, 'Lower Subansiri', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(24, 'Upper Subansiri', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(25, 'West Siang', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(26, 'East Siang', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(27, 'Siang', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(28, 'Upper Siang', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(29, 'Lower Siang', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(30, 'Lower Dibang Valley', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(31, 'Dibang Valley', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(32, 'Anjaw', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(33, 'Lohit', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(34, 'Namsai', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(35, 'Changlang', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(36, 'Tirap', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(37, 'Longding', '', 3, 'active', 0, '2024-03-31', '2024-03-30 18:30:41'),
(38, 'Baksa', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(39, 'Barpeta', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(40, 'Biswanath', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(41, 'Bongaigaon', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(42, 'Cachar', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(43, 'Charaideo', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(44, 'Chirang', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(45, 'Darrang', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(46, 'Dhemaji', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(47, 'Dhubri', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(48, 'Dibrugarh', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(49, 'Dima Hasao', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(50, 'Goalpara', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(51, 'Golaghat', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(52, 'Hailakandi', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(53, 'Hojai', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(54, 'Jorhat', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(55, 'Kamrup', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(56, 'Kamrup Metropolitan', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(57, 'Karbi Anglong', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(58, 'Karimganj', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(59, 'Kokrajhar', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(60, 'Lakhimpur', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(61, 'Majuli', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(62, 'Morigaon', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(63, 'Nagaon', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(64, 'Nalbari', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(65, 'Sivasagar', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(66, 'Sonitpur', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(67, 'South Salmara-Mankachar', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(68, 'Tinsukia', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(69, 'Udalguri', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(70, 'West Karbi Anglong', '', 4, 'active', 0, '2024-03-31', '2024-03-30 18:31:35'),
(71, 'Araria', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(72, 'Arwal', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(73, 'Aurangabad', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(74, 'Banka', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(75, 'Begusarai', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(76, 'Bhagalpur', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(77, 'Bhojpur', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(78, 'Buxar', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(79, 'Darbhanga', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(80, 'East Champaran (Motihari)', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(81, 'Gaya', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(82, 'Gopalganj', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(83, 'Jamui', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(84, 'Jehanabad', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(85, 'Kaimur (Bhabua)', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(86, 'Katihar', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(87, 'Khagaria', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(88, 'Kishanganj', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(89, 'Lakhisarai', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(90, 'Madhepura', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(91, 'Madhubani', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(92, 'Munger (Monghyr)', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(93, 'Muzaffarpur', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(94, 'Nalanda', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(95, 'Nawada', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(96, 'Patna', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(97, 'Purnia (Purnea)', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(98, 'Rohtas', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(99, 'Saharsa', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(100, 'Samastipur', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(101, 'Saran', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(102, 'Sheikhpura', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(103, 'Sheohar', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(104, 'Sitamarhi', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(105, 'Siwan', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(106, 'Supaul', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(107, 'Vaishali', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(108, 'West Champaran', '', 5, 'active', 0, '2024-03-31', '2024-03-30 18:33:06'),
(109, 'Chandigarh', '', 6, 'active', 0, '2024-03-31', '2024-03-30 18:34:37'),
(110, 'Balod', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(111, 'Baloda Bazar', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(112, 'Balrampur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(113, 'Bastar', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(114, 'Bemetara', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(115, 'Bijapur', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(116, 'Bilaspur', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(117, 'Dantewada (South Bastar)', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(118, 'Dhamtari', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(119, 'Durg', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(120, 'Gariyaband', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(121, 'Janjgir-Champa', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(122, 'Jashpur', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(123, 'Kabirdham (Kawardha)', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(124, 'Kanker (North Bastar)', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(125, 'Kondagaon', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(126, 'Korba', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(127, 'Koriya', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(128, 'Mahasamund', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(129, 'Mungeli', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(130, 'Narayanpur', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(131, 'Raigarh', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(132, 'Raipur', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(133, 'Rajnandgaon', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(134, 'Sukma', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(135, 'Surajpur', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(136, 'Surguja', '', 7, 'active', 0, '2024-03-31', '2024-03-30 18:37:40'),
(137, 'Dadra and Nagar Haveli', '', 8, 'active', 0, '2024-03-31', '2024-03-30 18:37:49'),
(138, 'Daman', '', 8, 'active', 0, '2024-03-31', '2024-03-30 18:37:49'),
(139, 'Diu', '', 8, 'active', 0, '2024-03-31', '2024-03-30 18:37:49'),
(140, 'Central Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(141, 'East Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(142, 'New Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(143, 'North Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(144, 'North East Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(145, 'North West Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(146, 'Shahdara', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(147, 'South Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(148, 'South East Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(149, 'South West Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(150, 'West Delhi', '', 10, 'active', 0, '2024-03-31', '2024-03-30 19:15:09'),
(151, 'North Goa', '', 11, 'active', 0, '2024-03-31', '2024-03-30 19:17:24'),
(152, 'South Goa', '', 11, 'active', 0, '2024-03-31', '2024-03-30 19:17:24'),
(153, 'Ahmedabad', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(154, 'Amreli', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(155, 'Anand', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(156, 'Aravalli', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(157, 'Banaskantha (Palanpur)', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(158, 'Bharuch', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(159, 'Bhavnagar', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(160, 'Botad', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(161, 'Chhota Udaipur', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(162, 'Dahod', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(163, 'Dang', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(164, 'Devbhoomi Dwarka', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(165, 'Gandhinagar', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(166, 'Gir Somnath', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(167, 'Jamnagar', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(168, 'Junagadh', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(169, 'Kheda (Nadiad)', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(170, 'Kutch (Bhuj)', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(171, 'Mahisagar', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(172, 'Mehsana', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(173, 'Morbi', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(174, 'Narmada (Rajpipla)', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(175, 'Navsari', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(176, 'Panchmahal (Godhra)', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(177, 'Patan', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(178, 'Porbandar', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(179, 'Rajkot', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(180, 'Sabarkantha (Himmatnagar)', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(181, 'Surat', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(182, 'Surendranagar', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(183, 'Tapi (Vyara)', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(184, 'Vadodara', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(185, 'Valsad', '', 12, 'active', 0, '2024-03-31', '2024-03-30 19:19:02'),
(186, 'Ambala', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(187, 'Bhiwani', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(188, 'Charkhi Dadri', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(189, 'Faridabad', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(190, 'Fatehabad', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(191, 'Gurugram (Gurgaon)', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(192, 'Hisar', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(193, 'Jhajjar', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(194, 'Jind', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(195, 'Kaithal', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(196, 'Karnal', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(197, 'Kurukshetra', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(198, 'Mahendragarh', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(199, 'Nuh', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(200, 'Palwal', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(201, 'Panchkula', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(202, 'Panipat', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(203, 'Rewari', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(204, 'Rohtak', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(205, 'Sirsa', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(206, 'Sonipat', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(207, 'Yamunanagar', '', 13, 'active', 0, '2024-03-31', '2024-03-30 19:23:26'),
(208, 'Bilaspur', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(209, 'Chamba', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(210, 'Hamirpur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(211, 'Kangra', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(212, 'Kinnaur', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(213, 'Kullu', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(214, 'Lahaul and Spiti', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(215, 'Mandi', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(216, 'Shimla', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(217, 'Sirmaur (Sirmour)', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(218, 'Solan', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(219, 'Una', '', 14, 'active', 0, '2024-03-31', '2024-03-30 19:28:45'),
(220, 'Anantnag', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(221, 'Bandipora', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(222, 'Baramulla', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(223, 'Budgam', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(224, 'Doda', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(225, 'Ganderbal', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(226, 'Jammu', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(227, 'Kathua', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(228, 'Kishtwar', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(229, 'Kulgam', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(230, 'Kupwara', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(231, 'Poonch', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(232, 'Pulwama', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(233, 'Rajouri', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(234, 'Ramban', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(235, 'Reasi', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(236, 'Samba', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(237, 'Shopian', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(238, 'Srinagar', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(239, 'Udhampur', '', 15, 'active', 0, '2024-03-31', '2024-03-30 19:33:41'),
(240, 'Bokaro', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(241, 'Chatra', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(242, 'Deoghar', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(243, 'Dhanbad', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(244, 'Dumka', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(245, 'East Singhbhum', '917421.jpeg', 16, 'active', 0, '2024-03-31', '2024-04-15 18:30:00'),
(246, 'Garhwa', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(247, 'Giridih', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(248, 'Godda', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(249, 'Gumla', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(250, 'Hazaribagh', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(251, 'Jamtara', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(252, 'Khunti', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(253, 'Koderma', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(254, 'Latehar', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(255, 'Lohardaga', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(256, 'Pakur', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(257, 'Palamu', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(258, 'Ramgarh', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(259, 'Ranchi', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(260, 'Sahibganj', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(261, 'Seraikela Kharsawan', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(262, 'Simdega', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(263, 'West Singhbhum', '', 16, 'active', 0, '2024-03-31', '2024-03-30 19:08:05'),
(264, 'Alappuzha', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(265, 'Ernakulam', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(266, 'Idukki', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(267, 'Kannur', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(268, 'Kasaragod', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(269, 'Kollam', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(270, 'Kottayam', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(271, 'Kozhikode', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(272, 'Malappuram', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(273, 'Palakkad', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(274, 'Pathanamthitta', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(275, 'Thiruvananthapuram', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(276, 'Thrissur', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(277, 'Wayanad', '', 18, 'active', 0, '2024-03-31', '2024-03-30 19:54:47'),
(278, 'Kargil', '', 19, 'active', 0, '2024-03-31', '2024-03-30 20:08:41'),
(279, 'Leh', '', 19, 'active', 0, '2024-03-31', '2024-03-30 20:08:41'),
(280, 'Lakshadweep', '', 20, 'active', 0, '2024-03-31', '2024-03-30 20:09:52'),
(281, 'Agar Malwa', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(282, 'Alirajpur', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(283, 'Anuppur', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(284, 'Ashoknagar', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(285, 'Balaghat', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(286, 'Barwani', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(287, 'Betul', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(288, 'Bhind', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(289, 'Bhopal', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(290, 'Burhanpur', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(291, 'Chhatarpur', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(292, 'Chhindwara', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(293, 'Damoh', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(294, 'Datia', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(295, 'Dewas', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(296, 'Dhar', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(297, 'Dindori', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(298, 'Guna', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(299, 'Gwalior', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(300, 'Harda', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(301, 'Hoshangabad', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(302, 'Indore', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(303, 'Jabalpur', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(304, 'Jhabua', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(305, 'Katni', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(306, 'Khandwa (East Nimar)', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(307, 'Khargone (West Nimar)', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(308, 'Mandla', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(309, 'Mandsaur', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(310, 'Morena', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(311, 'Narsinghpur', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(312, 'Neemuch', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(313, 'Niwari', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(314, 'Panna', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(315, 'Raisen', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(316, 'Rajgarh', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(317, 'Ratlam', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(318, 'Rewa', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(319, 'Sagar', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(320, 'Satna', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(321, 'Sehore', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(322, 'Seoni', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(323, 'Shahdol', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(324, 'Shajapur', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(325, 'Sheopur', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(326, 'Shivpuri', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(327, 'Sidhi', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(328, 'Singrauli', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(329, 'Tikamgarh', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(330, 'Ujjain', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(331, 'Umaria', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(332, 'Vidisha', '', 21, 'active', 0, '2024-03-31', '2024-03-30 20:10:41'),
(333, 'Ahmednagar', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(334, 'Akola', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(335, 'Amravati', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(336, 'Aurangabad', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(337, 'Beed', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(338, 'Bhandara', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(339, 'Buldhana', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(340, 'Chandrapur', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(341, 'Dhule', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(342, 'Gadchiroli', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(343, 'Gondia', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(344, 'Hingoli', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(345, 'Jalgaon', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(346, 'Jalna', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(347, 'Kolhapur', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(348, 'Latur', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(349, 'Mumbai City', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(350, 'Mumbai Suburban', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(351, 'Nagpur', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(352, 'Nanded', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(353, 'Nandurbar', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(354, 'Nashik', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(355, 'Osmanabad', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(356, 'Palghar', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(357, 'Parbhani', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(358, 'Pune', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(359, 'Raigad', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(360, 'Ratnagiri', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(361, 'Sangli', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(362, 'Satara', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(363, 'Sindhudurg', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(364, 'Solapur', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(365, 'Thane', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(366, 'Wardha', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(367, 'Washim', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(368, 'Yavatmal', '', 22, 'active', 0, '2024-03-31', '2024-03-30 20:11:25'),
(369, 'Bishnupur', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(370, 'Chandel', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(371, 'Churachandpur', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(372, 'Imphal East', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(373, 'Imphal West', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(374, 'Jiribam', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(375, 'Kakching', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(376, 'Kamjong', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(377, 'Kangpokpi', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(378, 'Noney', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(379, 'Pherzawl', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(380, 'Senapati', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(381, 'Tamenglong', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(382, 'Tengnoupal', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(383, 'Thoubal', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(384, 'Ukhrul', '', 23, 'active', 0, '2024-03-31', '2024-03-30 20:13:18'),
(385, 'East Garo Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(386, 'East Jaintia Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(387, 'East Khasi Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(388, 'North Garo Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(389, 'Ri-Bhoi', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(390, 'South Garo Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(391, 'South West Garo Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(392, 'South West Khasi Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(393, 'West Garo Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(394, 'West Jaintia Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(395, 'West Khasi Hills', '', 24, 'active', 0, '2024-03-31', '2024-03-30 20:14:02'),
(396, 'Aizawl', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(397, 'Champhai', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(398, 'Hnahthial', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(399, 'Khawzawl', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(400, 'Lawngtlai', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(401, 'Lunglei', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(402, 'Mamit', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(403, 'Saiha', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(404, 'Saitual', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(405, 'Serchhip', '', 25, 'active', 0, '2024-03-31', '2024-03-30 20:14:34'),
(406, 'Dimapur', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(407, 'Kiphire', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(408, 'Kohima', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(409, 'Longleng', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(410, 'Mokokchung', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(411, 'Mon', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(412, 'Peren', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(413, 'Phek', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(414, 'Tuensang', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(415, 'Wokha', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(416, 'Zunheboto', '', 26, 'active', 0, '2024-03-31', '2024-03-30 20:15:45'),
(417, 'Angul', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(418, 'Balangir', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(419, 'Balasore', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(420, 'Bargarh', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(421, 'Bhadrak', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(422, 'Boudh', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(423, 'Cuttack', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(424, 'Deogarh', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(425, 'Dhenkanal', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(426, 'Gajapati', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(427, 'Ganjam', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(428, 'Jagatsinghapur', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(429, 'Jajpur', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(430, 'Jharsuguda', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(431, 'Kalahandi', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(432, 'Kandhamal', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(433, 'Kendrapara', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(434, 'Kendujhar (Keonjhar)', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(435, 'Khordha', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(436, 'Koraput', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(437, 'Malkangiri', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(438, 'Mayurbhanj', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(439, 'Nabarangpur', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(440, 'Nayagarh', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(441, 'Nuapada', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(442, 'Puri', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(443, 'Rayagada', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(444, 'Sambalpur', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(445, 'Subarnapur (Sonepur)', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(446, 'Sundargarh', '', 27, 'active', 0, '2024-03-31', '2024-03-30 20:16:31'),
(447, 'Karaikal', '', 28, 'active', 0, '2024-03-31', '2024-03-30 20:17:02'),
(448, 'Mahe', '', 28, 'active', 0, '2024-03-31', '2024-03-30 20:17:02'),
(449, 'Puducherry', '', 28, 'active', 0, '2024-03-31', '2024-03-30 20:17:02'),
(450, 'Yanam', '', 28, 'active', 0, '2024-03-31', '2024-03-30 20:17:02'),
(451, 'Amritsar', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(452, 'Barnala', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(453, 'Bathinda', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(454, 'Faridkot', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(455, 'Fatehgarh Sahib', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(456, 'Fazilka', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(457, 'Firozpur', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(458, 'Gurdaspur', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(459, 'Hoshiarpur', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(460, 'Jalandhar', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(461, 'Kapurthala', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(462, 'Ludhiana', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(463, 'Mansa', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(464, 'Moga', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(465, 'Muktsar', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(466, 'Pathankot', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(467, 'Patiala', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(468, 'Rupnagar', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(469, 'Sahibzada Ajit Singh Nagar (Mohali)', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(470, 'Sangrur', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(471, 'Shahid Bhagat Singh Nagar (Nawanshahr)', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(472, 'Sri Muktsar Sahib (Muktsar)', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(473, 'Tarn Taran', '', 29, 'active', 0, '2024-03-31', '2024-03-30 20:17:32'),
(474, 'Ajmer', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(475, 'Alwar', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(476, 'Banswara', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(477, 'Baran', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(478, 'Barmer', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(479, 'Bharatpur', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(480, 'Bhilwara', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(481, 'Bikaner', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(482, 'Bundi', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(483, 'Chittorgarh', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(484, 'Churu', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(485, 'Dausa', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(486, 'Dholpur', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(487, 'Dungarpur', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(488, 'Hanumangarh', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(489, 'Jaipur', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(490, 'Jaisalmer', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(491, 'Jalore', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(492, 'Jhalawar', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(493, 'Jhunjhunu', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(494, 'Jodhpur', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(495, 'Karauli', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(496, 'Kota', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(497, 'Nagaur', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(498, 'Pali', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(499, 'Pratapgarh', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(500, 'Rajsamand', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(501, 'Sawai Madhopur', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(502, 'Sikar', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(503, 'Sirohi', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(504, 'Sri Ganganagar', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(505, 'Tonk', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(506, 'Udaipur', '', 30, 'active', 0, '2024-03-31', '2024-03-30 20:18:34'),
(507, 'East Sikkim', '', 31, 'active', 0, '2024-03-31', '2024-03-30 20:19:11'),
(508, 'North Sikkim', '', 31, 'active', 0, '2024-03-31', '2024-03-30 20:19:11'),
(509, 'South Sikkim', '', 31, 'active', 0, '2024-03-31', '2024-03-30 20:19:11'),
(510, 'West Sikkim', '', 31, 'active', 0, '2024-03-31', '2024-03-30 20:19:11'),
(511, 'Ariyalur', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(512, 'Chengalpattu', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(513, 'Chennai', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(514, 'Coimbatore', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(515, 'Cuddalore', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(516, 'Dharmapuri', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(517, 'Dindigul', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(518, 'Erode', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(519, 'Kallakurichi', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(520, 'Kanchipuram', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(521, 'Kanyakumari', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(522, 'Karur', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(523, 'Krishnagiri', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(524, 'Madurai', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(525, 'Mayiladuthurai', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(526, 'Nagapattinam', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(527, 'Namakkal', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(528, 'Nilgiris', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(529, 'Perambalur', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(530, 'Pudukkottai', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(531, 'Ramanathapuram', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(532, 'Ranipet', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(533, 'Salem', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(534, 'Sivaganga', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(535, 'Tenkasi', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(536, 'Thanjavur', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(537, 'Theni', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(538, 'Thoothukudi (Tuticorin)', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(539, 'Tiruchirappalli', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(540, 'Tirunelveli', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(541, 'Tirupathur', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(542, 'Tiruppur', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(543, 'Tiruvallur', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(544, 'Tiruvannamalai', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(545, 'Tiruvarur', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(546, 'Vellore', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(547, 'Viluppuram', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(548, 'Virudhunagar', '', 32, 'active', 0, '2024-03-31', '2024-03-30 20:19:41'),
(549, 'Adilabad', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(550, 'Bhadradri Kothagudem', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(551, 'Hyderabad', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(552, 'Jagtial', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(553, 'Jangaon', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(554, 'Jayashankar Bhupalapally', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(555, 'Jogulamba Gadwal', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(556, 'Kamareddy', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(557, 'Karimnagar', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(558, 'Khammam', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(559, 'Kumuram Bheem', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(560, 'Mahabubabad', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(561, 'Mahabubnagar', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(562, 'Mancherial', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(563, 'Medak', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(564, 'Medchal–Malkajgiri', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(565, 'Mulugu', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(566, 'Nagarkurnool', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(567, 'Nalgonda', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(568, 'Narayanpet', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(569, 'Nirmal', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(570, 'Nizamabad', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(571, 'Peddapalli', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(572, 'Rajanna Sircilla', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(573, 'Rangareddy', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(574, 'Sangareddy', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(575, 'Siddipet', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(576, 'Suryapet', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(577, 'Vikarabad', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(578, 'Wanaparthy', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(579, 'Warangal Rural', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(580, 'Warangal Urban', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(581, 'Yadadri Bhuvanagiri', '', 33, 'active', 0, '2024-03-31', '2024-03-30 20:20:25'),
(582, 'Dhalai', '', 34, 'active', 0, '2024-03-31', '2024-03-30 20:21:10'),
(583, 'Gomati', '', 34, 'active', 0, '2024-03-31', '2024-03-30 20:21:10'),
(584, 'Khowai', '', 34, 'active', 0, '2024-03-31', '2024-03-30 20:21:10'),
(585, 'North Tripura', '', 34, 'active', 0, '2024-03-31', '2024-03-30 20:21:10'),
(586, 'Sepahijala', '', 34, 'active', 0, '2024-03-31', '2024-03-30 20:21:10'),
(587, 'South Tripura', '', 34, 'active', 0, '2024-03-31', '2024-03-30 20:21:10'),
(588, 'Unakoti', '', 34, 'active', 0, '2024-03-31', '2024-03-30 20:21:10'),
(589, 'West Tripura', '', 34, 'active', 0, '2024-03-31', '2024-03-30 20:21:10'),
(590, 'Agra', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(591, 'Aligarh', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(592, 'Ambedkar Nagar', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(593, 'Amethi', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(594, 'Amroha', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(595, 'Auraiya', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(596, 'Ayodhya', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(597, 'Azamgarh', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(598, 'Baghpat', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(599, 'Bahraich', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(600, 'Ballia', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(601, 'Balrampur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(602, 'Banda', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(603, 'Barabanki', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(604, 'Bareilly', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(605, 'Basti', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(606, 'Bhadohi', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(607, 'Bijnor', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(608, 'Budaun', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(609, 'Bulandshahr', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(610, 'Chandauli', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(611, 'Chitrakoot', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(612, 'Deoria', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(613, 'Etah', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(614, 'Etawah', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(615, 'Farrukhabad', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(616, 'Fatehpur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(617, 'Firozabad', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(618, 'Gautam Buddh Nagar', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(619, 'Ghaziabad', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(620, 'Ghazipur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(621, 'Gonda', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(622, 'Gorakhpur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(623, 'Hamirpur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(624, 'Hapur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(625, 'Hardoi', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(626, 'Hathras', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(627, 'Jalaun', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(628, 'Jaunpur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(629, 'Jhansi', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(630, 'Kannauj', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(631, 'Kanpur Dehat', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(632, 'Kanpur Nagar', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(633, 'Kasganj', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(634, 'Kaushambi', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(635, 'Kushinagar', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(636, 'Lakhimpur Kheri', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(637, 'Lalitpur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(638, 'Lucknow', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(639, 'Maharajganj', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(640, 'Mahoba', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(641, 'Mainpuri', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(642, 'Mathura', '', 35, 'active', 1, '2024-03-31', '2024-04-25 19:00:44'),
(643, 'Mau', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(644, 'Meerut', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(645, 'Mirzapur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(646, 'Moradabad', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(647, 'Muzaffarnagar', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(648, 'Pilibhit', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(649, 'Pratapgarh', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(650, 'Prayagraj', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(651, 'Rae Bareli', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(652, 'Rampur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(653, 'Saharanpur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(654, 'Sambhal', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(655, 'Sant Kabir Nagar', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(656, 'Shahjahanpur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(657, 'Shamli', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(658, 'Shravasti', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(659, 'Siddharthnagar', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52');
INSERT INTO `tabl_districts` (`id`, `district_name`, `image`, `state_id`, `status`, `is_deleted`, `date_added`, `date_updated`) VALUES
(660, 'Sitapur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(661, 'Sonbhadra', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(662, 'Sultanpur', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(663, 'Unnao', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(664, 'Varanasi', '', 35, 'active', 0, '2024-03-31', '2024-03-30 20:21:52'),
(665, 'Almora', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(666, 'Bageshwar', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(667, 'Chamoli', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(668, 'Champawat', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(669, 'Dehradun', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(670, 'Haridwar', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(671, 'Nainital', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(672, 'Pauri Garhwal', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(673, 'Pithoragarh', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(674, 'Rudraprayag', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(675, 'Tehri Garhwal', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(676, 'Udham Singh Nagar', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(677, 'Uttarkashi', '', 36, 'active', 0, '2024-03-31', '2024-03-30 19:46:59'),
(678, 'Alipurduar', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(679, 'Bankura', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(680, 'Birbhum', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(681, 'Cooch Behar', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(682, 'Dakshin Dinajpur (South Dinajpur)', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(683, 'Darjeeling', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(684, 'Hooghly', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(685, 'Howrah', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(686, 'Jalpaiguri', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(687, 'Jhargram', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(688, 'Kalimpong', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(689, 'Kolkata', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(690, 'Malda', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(691, 'Murshidabad', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(692, 'Nadia', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(693, 'North 24 Parganas', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(694, 'Paschim Medinipur (West Medinipur)', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(695, 'Paschim Bardhaman (West Bardhaman)', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(696, 'Purba Bardhaman (East Bardhaman)', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(697, 'Purba Medinipur (East Medinipur)', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(698, 'Purulia', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(699, 'South 24 Parganas', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(700, 'Uttar Dinajpur (North Dinajpur)', '', 37, 'active', 0, '2024-03-31', '2024-03-30 19:47:10'),
(701, 'Bagalkot', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(702, 'Ballari (Bellary)', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(703, 'Belagavi (Belgaum)', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(704, 'Bengaluru Rural', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(705, 'Bengaluru Urban', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(706, 'Bidar', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(707, 'Chamarajanagar', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(708, 'Chikkaballapur', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(709, 'Chikkamagaluru (Chikmagalur)', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(710, 'Chitradurga', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(711, 'Dakshina Kannada', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(712, 'Davanagere', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(713, 'Dharwad', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(714, 'Gadag', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(715, 'Hassan', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(716, 'Haveri', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(717, 'Kalaburagi (Gulbarga)', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(718, 'Kodagu', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(719, 'Kolar', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(720, 'Koppal', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(721, 'Mandya', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(722, 'Mysuru (Mysore)', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(723, 'Raichur', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(724, 'Ramanagara', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(725, 'Shivamogga (Shimoga)', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(726, 'Tumakuru (Tumkur)', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(727, 'Udupi', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(728, 'Uttara Kannada (Karwar)', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(729, 'Vijayapura (Bijapur)', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(730, 'Yadgir', '', 17, 'active', 0, '2024-03-31', '2024-03-30 19:51:42'),
(731, 'Dadra and Nagar Haveli', '', 9, 'active', 0, '2024-03-31', '2024-03-30 20:06:19'),
(732, 'Daman', '', 9, 'active', 0, '2024-03-31', '2024-03-30 20:06:19'),
(733, 'Diu', '', 9, 'active', 0, '2024-03-31', '2024-03-30 20:06:19'),
(734, 'dsfsdf', '', 16, 'active', 1, '2024-04-01', '2024-04-16 10:23:25'),
(735, 'dsfsdf', '', 16, 'active', 1, '2024-04-01', '2024-04-16 10:23:21'),
(736, 'test', '', 16, 'active', 1, '2024-04-01', '2024-04-16 10:23:18'),
(737, 'test', '', 16, 'active', 1, '2024-04-01', '2024-04-16 10:23:15'),
(738, 'Ayush', '', 16, 'active', 1, '2024-04-01', '2024-04-11 06:42:56'),
(739, 'Ayush', '', 16, 'active', 1, '2024-04-01', '2024-04-11 06:42:59'),
(740, 'trest2', '', 16, 'active', 1, '2024-04-01', '2024-04-16 10:23:11'),
(741, 'trest2', '', 16, 'inactive', 1, '2024-04-01', '2024-04-16 10:23:07'),
(742, 'test', '916068.png', 16, 'active', 1, '2024-04-16', '2024-04-16 10:52:03'),
(743, 'Mathura', '704589.jpeg', 35, 'active', 0, '2024-04-19', '2024-04-19 18:30:00');

-- --------------------------------------------------------

--
-- Table structure for table `tabl_services`
--

DROP TABLE IF EXISTS `tabl_services`;
CREATE TABLE IF NOT EXISTS `tabl_services` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_services`
--

INSERT INTO `tabl_services` (`id`, `name`, `image`, `status`, `created_at`) VALUES
(4, 'Beauty Services', '120011.jpg', 1, '2024-05-08 18:30:00'),
(3, 'Home Cleaning', '542113.jpg', 1, '2024-05-08 18:30:00'),
(9, 'Washroom cleaning', '561103.jpg', 1, '2024-05-25 18:30:00'),
(8, 'Men Grooming', '573806.jpg', 1, '2024-05-25 18:30:00'),
(10, 'Learn Driving', '977795.jpg', 1, '2024-05-25 18:30:00'),
(11, 'Trainer', '64289.jpg', 1, '2024-05-25 18:30:00'),
(12, 'Tailor', '553537.jpg', 1, '2024-05-25 18:30:00'),
(13, 'AC mechanic', '552767.jpg', 1, '2024-05-25 18:30:00'),
(14, 'Electrician', '706217.jpeg', 1, '2024-05-25 18:30:00'),
(15, 'RO mechanic', '105234.jpg', 1, '2024-05-25 18:30:00'),
(16, 'Female staff', '549196.png', 1, '2025-01-07 18:30:00'),
(17, 'Male staff ', '17277.jpg', 1, '2025-01-07 18:30:00');

-- --------------------------------------------------------

--
-- Table structure for table `tabl_service_order`
--

DROP TABLE IF EXISTS `tabl_service_order`;
CREATE TABLE IF NOT EXISTS `tabl_service_order` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `service_worker_id` int DEFAULT NULL,
  `worker_id` int DEFAULT NULL,
  `service_type_id` int DEFAULT NULL,
  `visite_date_time` timestamp NULL DEFAULT NULL,
  `status` tinyint(1) DEFAULT '1',
  `date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_service_order`
--

INSERT INTO `tabl_service_order` (`id`, `user_id`, `service_worker_id`, `worker_id`, `service_type_id`, `visite_date_time`, `status`, `date`) VALUES
(14, 28, 19, NULL, 8, '0000-00-00 00:00:00', 1, '2024-08-31 11:11:33'),
(15, 11, 24, NULL, 4, '2025-06-08 14:49:00', 1, '2025-06-01 14:49:33');

-- --------------------------------------------------------

--
-- Table structure for table `tabl_setting`
--

DROP TABLE IF EXISTS `tabl_setting`;
CREATE TABLE IF NOT EXISTS `tabl_setting` (
  `id` int NOT NULL AUTO_INCREMENT,
  `site_name` varchar(50) NOT NULL,
  `site_email` varchar(50) NOT NULL,
  `facebook` varchar(100) NOT NULL,
  `instagram` varchar(100) NOT NULL,
  `twitter` varchar(100) NOT NULL,
  `linkedin` varchar(100) NOT NULL,
  `whatsapp_number` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

--
-- Dumping data for table `tabl_setting`
--

INSERT INTO `tabl_setting` (`id`, `site_name`, `site_email`, `facebook`, `instagram`, `twitter`, `linkedin`, `whatsapp_number`) VALUES
(1, 'GRS', '<EMAIL>', 'https://facebook.com', '', '', '', '6393116990');

-- --------------------------------------------------------

--
-- Table structure for table `tabl_states`
--

DROP TABLE IF EXISTS `tabl_states`;
CREATE TABLE IF NOT EXISTS `tabl_states` (
  `state_id` int NOT NULL AUTO_INCREMENT,
  `state_name` varchar(100) NOT NULL,
  `state_code` varchar(5) DEFAULT NULL,
  `image` varchar(255) NOT NULL,
  `country_id` int DEFAULT NULL,
  `status` tinyint(1) DEFAULT '1' COMMENT '	1 = active, 0 = inactive	',
  `is_deleted` tinyint(1) DEFAULT '0',
  `date_added` date DEFAULT NULL,
  `date_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`state_id`),
  KEY `country_id` (`country_id`),
  KEY `idx_state_name` (`state_name`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_states`
--

INSERT INTO `tabl_states` (`state_id`, `state_name`, `state_code`, `image`, `country_id`, `status`, `is_deleted`, `date_added`, `date_updated`) VALUES
(1, 'Andaman and Nicobar Islands', 'AN', '', 1, 1, 0, '2024-03-30', '2024-04-01 06:51:57'),
(2, 'Andhra Pradesh', 'AP', '', 1, 1, 0, '2024-03-30', '2024-04-01 07:35:16'),
(3, 'Arunachal Pradesh', 'AR', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(4, 'Assam', 'AS', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(5, 'Bihar', 'BR', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(6, 'Chandigarh', 'CH', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(7, 'Chhattisgarh', 'CG', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(8, 'Dadra and Nagar Haveli', 'DN', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(9, 'Daman and Diu', 'DD', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(10, 'Delhi', 'DL', '459772.jpeg', 1, 1, 0, '2024-03-30', '2024-04-09 11:19:20'),
(11, 'Goa', 'GA', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(12, 'Gujarat', 'GJ', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(13, 'Haryana', 'HR', '368305.jpeg', 1, 1, 0, '2024-03-30', '2024-04-09 11:20:14'),
(14, 'Himachal Pradesh', 'HP', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(15, 'Jammu and Kashmir', 'JK', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(16, 'Jharkhand', 'JH', '565046.jpg', 1, 1, 0, '2024-03-30', '2024-04-09 11:18:41'),
(17, 'Karnataka', 'KA', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(18, 'Kerala', 'KL', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(19, 'Ladakh', 'LA', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(20, 'Lakshadweep', 'LD', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(21, 'Madhya Pradesh', 'MP', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(22, 'Maharashtra', 'MH', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(23, 'Manipur', 'MN', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(24, 'Meghalaya', 'ML', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(25, 'Mizoram', 'MZ', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(26, 'Nagaland', 'NL', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(27, 'Odisha', 'OD', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(28, 'Puducherry', 'PY', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(29, 'Punjab', 'PB', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(30, 'Rajasthan', 'RJ', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(31, 'Sikkim', 'SK', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(32, 'Tamil Nadu', 'TN', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(33, 'Telangana', 'TS', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(34, 'Tripura', 'TR', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(35, 'Uttar Pradesh', 'UP', '598440.jpg', 1, 1, 0, '2024-03-30', '2024-04-19 10:33:01'),
(36, 'Uttarakhand', 'UK', '', 1, 1, 0, '2024-03-30', '2024-03-30 17:59:24'),
(37, 'West Bengal', 'WB', '', 1, 1, 0, '2024-03-30', '2024-04-09 11:18:09');

-- --------------------------------------------------------

--
-- Table structure for table `tabl_users`
--

DROP TABLE IF EXISTS `tabl_users`;
CREATE TABLE IF NOT EXISTS `tabl_users` (
  `user_id` int NOT NULL AUTO_INCREMENT,
  `profile_img` varchar(100) DEFAULT NULL,
  `fullname` varchar(50) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) DEFAULT NULL,
  `phone_number` varchar(15) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `gender` varchar(20) DEFAULT NULL,
  `otp` int DEFAULT NULL,
  `otp_expiry` datetime DEFAULT NULL,
  `lastLogin` datetime DEFAULT NULL,
  `is_remembered` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 = uncheck , 1 = check',
  `own_code` varchar(50) DEFAULT NULL,
  `refral_code` int DEFAULT NULL,
  `address` text,
  `district` varchar(255) DEFAULT NULL,
  `state` varchar(255) DEFAULT NULL,
  `pincode` varchar(255) DEFAULT NULL,
  `date_added` date DEFAULT NULL,
  `status` tinyint(1) DEFAULT '1',
  `is_deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_users`
--

INSERT INTO `tabl_users` (`user_id`, `profile_img`, `fullname`, `email`, `password`, `phone_number`, `dob`, `gender`, `otp`, `otp_expiry`, `lastLogin`, `is_remembered`, `own_code`, `refral_code`, `address`, `district`, `state`, `pincode`, `date_added`, `status`, `is_deleted`) VALUES
(2, '1714392150.png', 'admin', '<EMAIL>', '$2y$10$3RnHqKhyI0FUWvtBfzKsSO2pJg2vk6wf3kux7MwdJslOP7Kf6DiMG', '9798098395', '0000-00-00', 'Male', NULL, NULL, '2024-04-23 15:33:59', 0, NULL, NULL, NULL, NULL, NULL, NULL, '2024-04-23', 1, 1),
(3, NULL, 'Payal', '<EMAIL>', '$2y$10$u2TaGQi1quai1.fgdalvH.Ztk4T41QwSXz3z7ad.PFzGY5t9aGO5a', '7011684313', NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '2024-04-25', 1, 1),
(4, '1715315806.jpg', 'Indal SIngh', '<EMAIL>', '$2y$10$UqfOQgeFnqiZLeg3wytWK.lefxKfhCLHOwkx9vEn4ZAUuqAop52S.', '9546576177', '2024-05-11', 'Male', NULL, NULL, '2024-05-24 18:12:12', 0, '885566', NULL, 'Jamhsedpur', 'Jamshedpur', 'Jharkhand', '832108', '2024-04-29', 1, 0),
(6, NULL, 'test', '<EMAIL>', '$2y$10$twCa31rwX.Vd83BTm7MhlugKF6gRbITrItAW7eVgx9BZlViPOs4iC', '9798098395', NULL, NULL, NULL, NULL, '2024-04-29 16:16:47', 0, NULL, NULL, NULL, NULL, NULL, NULL, '2024-04-29', 0, 1),
(11, NULL, 'Satyam pandey', '<EMAIL>', '$2y$10$nbyU2MJDOPmtad2leOMn5OlPLGsEDyhtQPFMKjTzgbOdeXXN45LRS', '6394302301', NULL, NULL, NULL, NULL, '2024-06-08 22:51:26', 0, '641133', NULL, NULL, NULL, NULL, NULL, '2024-05-23', 1, 0),
(16, NULL, 'Rishi', '<EMAIL>', '$2y$10$XV9MdHB8TZtMdWnykI42Oe9JoPEDv0iznyQ7b8ctrhO398XXJzrb6', '9534434177', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-05-31', 1, 0),
(17, NULL, 'Shivam', '<EMAIL>', '$2y$10$ZmlIrftyUsIXx0oxppmK8OZyT07YeC.ghjEfto0wAThwabblKvw6S', '6306027806', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-05-31', 1, 0),
(18, NULL, 'Nikhil', '<EMAIL>', '$2y$10$FEay7sj9cxG9BliWPoCT6.cAfFq2V0RVKqB5kchaahi/02Ay.vWSC', '6390884867', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-06-04', 1, 0),
(19, '1717514653.jpeg', 'PIYUSH SHARMA', '<EMAIL>', '$2y$10$ZeiU972ELIdwhQBa96AQr.VexFLa9smSZgV/GP3VelgufL.HsnTIi', '9889769999', '1989-06-10', 'Male', NULL, NULL, '2024-06-04 20:58:00', 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-06-04', 1, 0),
(20, NULL, 'Vijay', '<EMAIL>', '$2y$10$kRAIyGu.plsQ4W2AE1VGweKhbhVwaW/kZbBZC3AXskH2P9s3kZYNS', '9335199349', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-06-04', 1, 0),
(21, NULL, 'Vatan Kumar Pandey', '<EMAIL>', '$2y$10$B38XwjYX4EY5uLCk/xk1Ouw6Up7Id0XAauL4udzU21BQY6MmYsuae', '9868732254', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-06-04', 1, 0),
(22, NULL, 'Suraj Pal', '<EMAIL>', '$2y$10$y4fVHmKGM3jRXRSNub78kumD/iQvUUlnEvMmH.xvrYc6sn9EI5gIi', '9026205787', '0000-00-00', 'Male', NULL, NULL, NULL, 0, '111645', 0, NULL, NULL, NULL, NULL, '2024-06-08', 1, 0),
(23, '1717867438.webp', 'Ankit', '<EMAIL>', '$2y$10$JKzzQpvQ87LoEHJNvUqiU.1GVLUZP.n.h5xExDUaZG4rO9lTTkhv2', '6387745190', '2002-06-08', 'Male', NULL, NULL, NULL, 0, NULL, 0, 'Paliya baskheda kakwan kanpur dehat', 'Kanpur dehat ', 'Uttar Pradesh', '209202', '2024-06-08', 1, 0),
(24, '1718022812.jpg', 'Ansh Sachan', '<EMAIL>', '$2y$10$plxR56rWWa04Pd/jNBtKSelXOe9tHB3lYiVFLU60UVKPj8Gx5rExe', '6307768914', '2005-03-10', '', NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-06-10', 1, 0),
(25, NULL, 'Vishal Kudalkar', '<EMAIL>', '$2y$10$b1OA7Ce8Xu64RzE98UOfzuSDhB5saNp80.vNWaxhLmmWdD30Ze5yi', '9167327298', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-06-17', 1, 0),
(26, NULL, 'Krati Pandey', '<EMAIL>', '$2y$10$AA2yheE7kbNEWpqahyspt.uyD.4ASCjx7sBI21pi4Kwbj8UmIPDt6', '9555636620', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-08-05', 1, 0),
(27, NULL, 'shreya pandey', '<EMAIL>', '$2y$10$CqyxoHFvGesbjpEkVcQJUuh9PYUVlFHmO.llroolYLNxO/S1y9fbq', '7355757665', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-08-24', 1, 0),
(28, NULL, 'Arif', '<EMAIL>', '$2y$10$Oa1ZD1IBghqnhioZDI42heS92Hwdss0LP/KKJEUy3II3H95fwc62G', '6201166485', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-08-31', 1, 0),
(29, '1725102454.jpg', 'Shiva gautam', '<EMAIL>', '$2y$10$5dhiweFsZQxtu77Aw0Aqk.b.Ug1hKwYOWBeJ9cn8whPUMjewwxs4m', '9336511389', '2005-12-24', '', NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-08-31', 1, 0),
(30, '1725105161.jpg', 'Somnath', '<EMAIL>', '$2y$10$G9Yl8z37r2GUK3lzSCNnMOlKesITL.NVqyhLsaqZlQ7plzlS7YGrW', '7897391243', '2003-01-01', 'Male', NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-08-31', 1, 0),
(31, NULL, 'Ambar gupta', '<EMAIL>', '$2y$10$dtq832IVLzNd6k/ExppZeuwDxdd.OSnotZNJNMtZJU/1f0nxg87SK', '9935351177', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-09-04', 1, 0),
(32, NULL, 'Gaurav Singh', '<EMAIL>', '$2y$10$q4PaEBTPh7doyZs.fs4mmenVoCuecnUTXZZ23Ehnk0x2WhEQJyGOu', '9450395842', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-09-24', 1, 0),
(33, NULL, 'Himani Sharma', '<EMAIL>', '$2y$10$37rxs6YA2mPwdOxeNXFd8u672HBYkrd1faxiSmpjK8fuQmf3jQoFi', '9999999999', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-10-04', 1, 0),
(34, NULL, 'rehan', '<EMAIL>', '$2y$10$DN40vTD6f2FClYkjR.5yF.p65huMD0LlcZRr4I2NChSws.38x9vfK', '9988998899', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-10-05', 1, 0),
(35, NULL, 'Aman nishad', '<EMAIL>', '$2y$10$yJ9WOk4HtM6HhtzNIgFuI.zEBv2dxpJeUGLZWkvRS5aWkru1Y/.P6', '9532985953', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-10-10', 1, 0),
(36, NULL, 'Aditi', '<EMAIL>', '$2y$10$zj6Fcc978beo5ZZSsKo8Aub2ltcSs.8XqFsKDQtNT200JBThOWHuK', '6789654557', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-10-10', 1, 0),
(37, NULL, 'kumar', '<EMAIL>', '$2y$10$.SRW0iCunibHqhqZCJrmhe0g/xcQQRpio6nNZek9PYr56SJwJTE9m', '6372086082', NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2024-10-13', 1, 0);

-- --------------------------------------------------------

--
-- Table structure for table `tabl_wishlist`
--

DROP TABLE IF EXISTS `tabl_wishlist`;
CREATE TABLE IF NOT EXISTS `tabl_wishlist` (
  `wishlist_id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `worker_id` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`wishlist_id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_wishlist`
--

INSERT INTO `tabl_wishlist` (`wishlist_id`, `user_id`, `worker_id`, `created_at`, `status`) VALUES
(25, 1, 9, '2024-05-10 17:08:02', 0),
(26, 12, 9, '2024-05-24 07:05:21', 0),
(28, 4, 44, '2024-06-10 09:22:30', 0);

-- --------------------------------------------------------

--
-- Table structure for table `tabl_workers`
--

DROP TABLE IF EXISTS `tabl_workers`;
CREATE TABLE IF NOT EXISTS `tabl_workers` (
  `worker_id` int NOT NULL AUTO_INCREMENT,
  `profile_img` varchar(255) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `password` varchar(255) NOT NULL,
  `dob` date DEFAULT NULL,
  `gender` enum('m','f','o','') DEFAULT NULL,
  `state` varchar(100) NOT NULL,
  `city` varchar(100) NOT NULL,
  `address` varchar(255) NOT NULL,
  `pincode` varchar(10) NOT NULL,
  `idType` varchar(50) NOT NULL,
  `aadhaarNumber` varchar(20) DEFAULT NULL,
  `panNumber` varchar(20) DEFAULT NULL,
  `lastLogin` datetime DEFAULT NULL,
  `is_remembered` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 = uncheck, 1 = check',
  `date_added` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1 = active , 0 = inactive',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`worker_id`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_workers`
--

INSERT INTO `tabl_workers` (`worker_id`, `profile_img`, `name`, `email`, `phone`, `password`, `dob`, `gender`, `state`, `city`, `address`, `pincode`, `idType`, `aadhaarNumber`, `panNumber`, `lastLogin`, `is_remembered`, `date_added`, `status`, `is_deleted`) VALUES
(11, NULL, 'Rishi Kumar', '<EMAIL>', '9534434177', '$2y$10$GEkuIFjZwS1Ly3nhCM72FuHKujRYO40zLZWKm01S5mpWE.4tum6qq', '2001-08-15', NULL, '10', 'Badarpur', 'Badarpur, Molar Band Extension', '110044', 'aadhaar', '************', '', NULL, 0, '2024-05-28 18:30:00', 1, 0),
(12, NULL, '', '<EMAIL>', '6394302301', '$2y$10$x5s1f2C3QCQ4M2Vx2Gn11ON85GmBUYTckYAheH94w7Za8HAFZAd1m', '1990-05-01', NULL, '35', 'Kanpur nagar', '116/385', '208019', 'aadhaar', '************', '', '2024-10-14 16:18:23', 0, '2024-05-28 18:30:00', 1, 0),
(13, NULL, 'Amit Kumar', '<EMAIL>', '9598976329', '$2y$10$nC84.8Gh678Ut0GJ8Ukf8uuCTUl0PykGsQIPaozNPwXinQXFQ49nS', '1992-09-17', 'm', '35', 'Kanpur', 'DT armapur', '208009', 'aadhaar', '************', '', '2024-06-03 10:44:20', 0, '2024-06-02 18:30:00', 1, 0),
(14, NULL, 'Vinod kumar sharma', '<EMAIL>', '9598641398', '$2y$10$cPplbQQ9vReK9p1YA1aBr.A.u7Xp/N8xalx8D03Zr.6pgETJBDJ8e', '1982-01-01', NULL, '35', 'Kanpur nagar', 'Kanpur', '208019', 'aadhaar', '************', '', '2024-06-03 13:08:40', 0, '2024-06-02 18:30:00', 1, 0),
(15, NULL, 'Ram babu', '<EMAIL>', '8604218741', '$2y$10$CDAcGWQns9QSWYjg0o5yiuJG/Se1YQeW6PycBvRaTITzCD61Fi/OK', '1971-07-19', NULL, '35', 'Kanpur nagar', '116/179 Rawatpur', '208019', 'aadhaar', '************', '', '2024-06-03 12:24:46', 0, '2024-06-02 18:30:00', 1, 0),
(16, NULL, 'Deepika arya', '<EMAIL>', '7678869136', '$2y$10$B4CN.6sZQp8w9S7Vck2VROoYSNhMror7nhUxaywir8So07RQBiCue', '1990-01-01', NULL, '35', 'Kanpur nagar', 'Kanpur', '208019', 'aadhaar', '************', '', '2024-06-03 12:40:05', 0, '2024-06-02 18:30:00', 1, 0),
(17, NULL, 'Mukesh kumar', '<EMAIL>', '8574678954', '$2y$10$TFyiDsuhj/icNIAR0PRZ1e0Pc6ITrp74zwHQEFiSM3vcWKGV5PNQq', '1992-07-15', NULL, '35', 'Kanpur nagar', '116/423 A Rawatpur', '208019', 'aadhaar', '************', '', '2024-06-03 12:46:32', 0, '2024-06-02 18:30:00', 1, 0),
(18, NULL, 'Amit', '<EMAIL>', '8953292456', '$2y$10$ZRg5G6n54W5r2X/HZp6k3ufwuepWk4bhzr.UJywWQqddzkCIbQQU.', '1989-01-01', NULL, '35', 'Kanpur nagar', '116/651 keshav nagar', '208019', 'aadhaar', '************', '', '2024-06-03 12:51:21', 0, '2024-06-02 18:30:00', 1, 0),
(19, NULL, 'Shyam singh', '<EMAIL>', '9807393368', '$2y$10$27pvv91EIfLbqB3BwUqKTOAmVbP6N1iYP0GIvyIu2nYxxUFjSL/q.', NULL, NULL, '35', 'Kanpur nagar', 'Rawatpur', '208019', 'aadhaar', '************', '', '2024-06-03 13:17:14', 0, '2024-06-02 18:30:00', 1, 0),
(20, NULL, 'Rakesh kumar tiwari', '<EMAIL>', '7906729556', '$2y$10$X6o2yAtjxnK0INJJNwMn4u1/3J8pwX/xRgdXcZbuWySmvBWtjGE1m', '1992-06-10', NULL, '35', 'Kanpur nagar', '116/654', '208019', 'aadhaar', '************', '', NULL, 0, '2024-06-02 18:30:00', 1, 0),
(21, NULL, 'Mukesh kumar trivedi', '<EMAIL>', '9219756316', '$2y$10$rckesWi/9NWo/bDvneSp0ufzJGTVOEwbNudpxhvgg7hmoUgbRlEum', '1986-01-01', NULL, '35', 'Kanpur nagar', '116/370', '208019', 'aadhaar', '************', '', '2024-06-04 09:39:35', 0, '2024-06-03 18:30:00', 1, 0),
(22, NULL, 'Raja singh', '<EMAIL>', '8090991987', '$2y$10$vhQMXC2HLxFGpRsYbegwPeQpMGmHw5t9iFKKh/yKKA/tb8wycSLF.', '1987-05-07', NULL, '35', 'Kanpur nagar', '116/11', '208019', 'aadhaar', '************', '', NULL, 0, '2024-06-03 18:30:00', 1, 0),
(23, NULL, 'Neeraj chauhan', '<EMAIL>', '9598686414', '$2y$10$ql5NLO30xzimB.oarPBesOCPhoOdEA58pDoYzy5igxGQwBftvkzV6', '1993-08-22', NULL, '35', 'Kanpur nagar', '116/179', '208019', 'aadhaar', '************', '', '2024-06-04 14:09:24', 0, '2024-06-03 18:30:00', 1, 0),
(24, NULL, 'Amit kumar', '<EMAIL>', '7983594250', '$2y$10$5x88d/nZ4kMeDgJVJ7DHJeMdaOwiKtHFBBYsnIBTG3JcFIe2KYd9a', '1988-09-02', 'm', '35', 'Kanpur nagar', 'Kalyanpur,Chaubepur Kanpur', '208017', 'aadhaar', '************', '', '2024-06-04 12:57:51', 0, '2024-06-03 18:30:00', 1, 0),
(25, NULL, 'Vatan kr pandey', '<EMAIL>', '9868732254', '$2y$10$te6RgWBPXF8/xQSjgYwdiu0iJ97lVDQ/V2SRPdiS6IlWDuhCv4j8e', '1998-09-08', NULL, '35', 'Kalyanpur, chaubepur, Kanpur dehat', '116 Rawatpur', '208017', 'aadhaar', '************', '', '2024-06-04 12:51:34', 0, '2024-06-03 18:30:00', 1, 0),
(26, NULL, 'Gyan Babu Dwivedi', '<EMAIL>', '7905957099', '$2y$10$Id4Oz.0Xud8TLAR2BJQ3aeLAe.PBVLWNGmUG28Tpp4WKlZvRne8kG', '1968-06-15', NULL, '35', 'Kanpur nagar', 'Rawatpur', '208019', 'aadhaar', '************', '', '2024-06-04 13:04:30', 0, '2024-06-03 18:30:00', 1, 0),
(27, NULL, 'Deepak Kushwah', '<EMAIL>', '7269010216', '$2y$10$iMQFoqs5dl.wJNvowd1E.eZE8P5WWts4iKZqdRSa6DSVMoxL6OyNW', '1991-01-01', NULL, '35', 'Kanpur nagar', '116/607', '208019', 'aadhaar', '************', '', '2024-06-04 13:11:09', 0, '2024-06-03 18:30:00', 1, 0),
(28, NULL, 'Nikhil', '<EMAIL>', '6390884867', '$2y$10$qkwygGqZEjseaNwV4hYHW.Le971TmQSQtfFwNQMMQ6rHXmGHVRuJi', '2005-09-14', NULL, '35', 'Kanpur nagar', '116/37', '208019', 'aadhaar', '************', '', '2024-06-04 13:16:28', 0, '2024-06-03 18:30:00', 1, 0),
(29, NULL, 'Fahim khan', '<EMAIL>', '8419062162', '$2y$10$TZV41mJRXzoS/Y6h2DpJweD6cVjmGUKOAjpBtkiFXa/q3DDlMcoH.', '1980-10-05', NULL, '35', 'Kanpur nagar', '116/68', '208019', 'aadhaar', '************', '', '2024-06-04 13:39:01', 0, '2024-06-03 18:30:00', 1, 0),
(30, NULL, 'Vijay kumar', '<EMAIL>', '9335199349', '$2y$10$JswtdKYHFn/enZAReNZodeaqAmKI1ROYV0mQRIm4IGlDc3m5hfNOK', '1983-12-15', 'm', '35', 'Kanpur nagar', '116/389', '208019', 'aadhaar', '************', '', '2024-06-04 21:29:02', 0, '2024-06-03 18:30:00', 1, 0),
(31, NULL, 'Arjun', '<EMAIL>', '8604608854', '$2y$10$Y1rAtMgHsLq6h8glsEcpKeUUQh4wy.3miUdo69ekbxaQh7gOtaLrq', '1976-01-01', NULL, '35', 'Kanpur nagar', 'Kanpur', '208019', 'aadhaar', '************', '', NULL, 0, '2024-06-03 18:30:00', 1, 0),
(32, NULL, 'Jagat Ram sahu', '<EMAIL>', '9369630488', '$2y$10$RpVFrWYiM66LrvMzx2aliO1ORgeF1JlYfZWgfOPcINKa3oi9Cb15C', '2002-01-15', NULL, '35', 'Kanpur nagar', 'Dholiya', '208019', 'aadhaar', '************', '', NULL, 0, '2024-06-05 18:30:00', 1, 0),
(33, NULL, 'Mohammad Nadim', '<EMAIL>', '7785841936', '$2y$10$Tp2eTkyIsfQcgzTl/mSExOQwDElV0iGGgZyVgulOZ4r2CPt4OWfw2', '1997-07-05', NULL, '35', 'Kanpur nagar', 'Rawatpur', '208019', 'aadhaar', '************', '', NULL, 0, '2024-06-06 18:30:00', 1, 0),
(34, NULL, 'Nand kishor', '<EMAIL>', '7379713621', '$2y$10$edrAulibQUDx203gjJCdiuS5RVMEviacX6er5z9saHK.y9v2N4fry', '2000-09-25', NULL, '35', 'Kanpur nagar', 'Rawatpur', '208019', 'aadhaar', '************', '', NULL, 0, '2024-06-06 18:30:00', 1, 0),
(35, NULL, 'Indal SIngh', '<EMAIL>', '8405010784', '$2y$10$0aiC3p5G0NGbhhyYTjZYKuX1gyrL.hWyloi2h4l6g/dF1/n5nhYGm', '2000-06-12', NULL, '16', 'Jamhsedpur', 'Gamharia', '832108', 'aadhaar', '************', '', '2024-06-10 15:46:23', 0, '2024-06-06 18:30:00', 1, 0),
(36, NULL, 'Ankit Kushwah', '<EMAIL>', '6388302378', '$2y$10$96YH.cIWV3XyzMWVhfrf4O1ojTy3Sq0MPzNIEagCgwuclPBmUek0O', '1998-11-09', NULL, '35', 'Kanpur nagar', '116/165', '208019', 'aadhaar', '************', '', NULL, 0, '2024-06-10 18:30:00', 1, 0),
(37, NULL, 'Baba salon', '<EMAIL>', '8423924195', '$2y$10$EZn0nPZpaEE883vfHk8fzerCWt/LxnBSBRYnhX77iTHwH4Wv2F3mq', '1982-02-02', 'm', '35', 'Kanpur nagar', 'Vijay nagar', '208019', 'aadhaar', '************', '', NULL, 0, '2024-06-10 18:30:00', 1, 0),
(38, NULL, 'Krati Pandey', '<EMAIL>', '9555636620', '$2y$10$nVApJ1BvGV/8qTYuXQ61FO724xlWNgzKa9Eq8adNP3lN.vaNOOJ4e', NULL, NULL, '35', 'Kanpur', '2/466 Azad Nagar kanpur', '208002', 'aadhaar', '************', '', NULL, 0, '2024-08-06 18:30:00', 1, 0),
(39, NULL, 'Pinki sonkar', '<EMAIL>', '8874408327', '$2y$10$AqplOwB8sAr.T8317rkXZ.41icujcoRrqjs/4qjY/CSYNX3P0cis2', '1990-01-01', NULL, '35', 'Kanpur nagar', '112/292 swaroop', '208019', 'aadhaar', '************', '', '2024-08-13 18:14:08', 0, '2024-08-12 18:30:00', 1, 0),
(40, NULL, 'Imran ansari', '<EMAIL>', '8840123092', '$2y$10$DAiZctXLcKCLc8aAR2DUzueGxuwpIOSC0ZoTRYE7xHR9TU7WjWHcG', '1995-06-07', NULL, '35', 'Kanpur nagar', 'Swaroop Nagar', '208019', 'aadhaar', '************', '', '2024-08-13 18:24:52', 0, '2024-08-12 18:30:00', 1, 0),
(41, NULL, 'Dev narayan', '<EMAIL>', '8707608072', '$2y$10$tGcnGwDpZgxqJwLp3PfmsetAnu2oZM1w1WQZly9nELFaMiJAQuJP.', '1963-01-01', NULL, '35', 'Kanpur nagar', 'Kanpur', '208019', 'aadhaar', '************', '', NULL, 0, '2024-08-13 18:30:00', 1, 0),
(42, NULL, 'Garima Gupta', '<EMAIL>', '8958013532', '$2y$10$8er8h.oZTHDId5DBnzJQneqvzbDc7mPReU8DDoYFm2XVKz34oGZ7y', NULL, NULL, '35', 'Kanpur nagar', 'Kanpur', '208019', 'aadhaar', '123456789012', '', '2024-08-26 18:41:23', 0, '2024-08-25 18:30:00', 1, 0),
(43, NULL, 'Ps motor', '<EMAIL>', '7704998998', '$2y$10$lgLhKvUIOQwrdeQXGdvzoO90jxQ8yk6Ok/.MR7OJbx5sfzMhuroK.', NULL, NULL, '35', 'Kanpur nagar', 'Kanpur', '208019', 'aadhaar', '123456789012', '', NULL, 0, '2024-08-26 18:30:00', 1, 0),
(44, NULL, 'Mridul Katiyar', '<EMAIL>', '8957144483', '$2y$10$e1gBH8bAn13Tplv9qvwp/ul9RWfkTsz1VwWNAdq8zA97sfRD/4UV.', NULL, NULL, '35', 'Kanpur', '117/Q/196', '208025', 'aadhaar', '************', '', NULL, 0, '2024-09-01 18:30:00', 1, 0),
(45, NULL, 'Shahid', '<EMAIL>', '8187927494', '$2y$10$Gl63cT/VU4iHTDgP5fHUceuoiWGWReJl0ByEKRu7LzhvdsnpnlcSK', '1996-05-05', NULL, '35', 'Kanpur Nagar', '116/815 rawatpur gaon kanpur', '208019', 'aadhaar', '************', '', NULL, 0, '2024-09-06 18:30:00', 1, 0),
(46, NULL, 'Shyam singh', '<EMAIL>', '9956614411', '$2y$10$m/dE6vuLjrwaztoTRe1xFesU6cLqF/zhPgTlH/038jQ2tcDH/SxWO', '1992-06-16', NULL, '35', 'Kanpur nagar', 'Kanpur', '209302', 'aadhaar', '************', '', '2024-09-08 15:07:21', 0, '2024-09-07 18:30:00', 1, 0),
(47, NULL, 'Pranav Srivastava', '<EMAIL>', '9473504530', '$2y$10$iOzRXQVofHKeFw85HTUbpOwUfF6ggaGHyABycapCW.XtIHIf4svYu', '1998-03-31', NULL, '35', 'Kanpur nagar', 'Juhi colony', '208014', 'aadhaar', '************', '', NULL, 0, '2024-09-07 18:30:00', 1, 0),
(48, NULL, 'Sui narayan', '<EMAIL>', '6388375612', '$2y$10$.vFBFEac/Lj5J8v0bBjYYOGi7KtkFnKxBawGuizw9KE61Wq.9k9OO', '1994-10-04', NULL, '35', 'Kanpur nagar', 'Nawabganj', '208019', 'aadhaar', '************', '', NULL, 0, '2024-10-03 18:30:00', 1, 0),
(49, NULL, 'Himani Sharma', '<EMAIL>', '9999999999', '$2y$10$ZKbCO.XgoYGfw6QH7aco8usy84pwygOeMPP4XYVJRk2QUpsNBvdcm', NULL, NULL, '13', 'Faridabad', 'Faridabad', '121003', 'aadhaar', '************', '', NULL, 0, '2024-10-04 18:30:00', 1, 0),
(50, NULL, 'Ramesh', '<EMAIL>', '9876543210', '$2y$10$h4GOt1kiOXhZzcD/0i9jzuYDad8N39bX3dN3EZto/uCPRA/UJoY1u', NULL, NULL, '35', '643', 'Mau kan', '208019', 'aadhaar', '************', '', NULL, 0, '2024-10-05 18:30:00', 1, 0),
(51, NULL, 'Himani Sharma', '<EMAIL>', '9999999990', '$2y$10$/MsppIWqvS5eyBZmzm/R7u6R4wYJ4HghegjHuj7jq6UUk8zTTPM7K', '0000-00-00', NULL, '13', '189', 'Faridabad', '121003', 'aadhaar', '************', '', NULL, 0, '2024-10-06 18:30:00', 1, 0),
(52, NULL, 'Aman', '<EMAIL>', '9532985953', '$2y$10$qZcqQRYNeELOW7RMR1JjU.R4r.FKLxc1gnibdYqjM4dIxjxOdB60q', '2003-03-19', NULL, '35', '632', 'Kanpur', '208019', 'aadhaar', '************', '', NULL, 0, '2024-10-09 18:30:00', 1, 0),
(53, NULL, 'Neeraj', '<EMAIL>', '9532163974', '$2y$10$Y.R.Y/qEntpyPNcfeyN1we89uZ4Up2JN8dQ4uWCVitwPvNPok269.', '1995-02-01', NULL, '35', '632', 'Kanpur', '208019', 'aadhaar', '************', '', '2024-10-14 16:17:47', 0, '2024-10-13 18:30:00', 1, 0),
(54, NULL, 'Amit mishra', '<EMAIL>', '7355574789', '$2y$10$d.wlbaj1k4b3OCvoWR081uAG/Z4Uo8bxbBtp2A7eoWSLNqZIT3Z36', '1980-04-01', NULL, '35', '632', 'Kanpur', '208001', 'aadhaar', '************', '', NULL, 0, '2024-10-13 18:30:00', 1, 0);

-- --------------------------------------------------------

--
-- Table structure for table `tabl_workers_category`
--

DROP TABLE IF EXISTS `tabl_workers_category`;
CREATE TABLE IF NOT EXISTS `tabl_workers_category` (
  `id` int NOT NULL AUTO_INCREMENT,
  `worker_id` int NOT NULL,
  `worker_image` varchar(255) DEFAULT NULL,
  `work_image` varchar(255) DEFAULT NULL,
  `cat_id` int DEFAULT NULL,
  `experience` varchar(50) DEFAULT NULL,
  `state` int DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `description` text,
  `remark` text,
  `status` int DEFAULT '0' COMMENT '0 = pending, 1 = approved , 2 = rejected',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `cat_status` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `worker_id` (`worker_id`)
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_workers_category`
--

INSERT INTO `tabl_workers_category` (`id`, `worker_id`, `worker_image`, `work_image`, `cat_id`, `experience`, `state`, `city`, `description`, `remark`, `status`, `created_at`, `updated_at`, `cat_status`) VALUES
(16, 10, '521025.png', '388205.png', 18, '2', 14, 'Faridabad', 'img3', '', 1, '2024-05-27 09:27:41', '2024-05-27 09:29:04', 1),
(20, 13, '469863.jpg', NULL, 12, '5', 35, 'Kanpur nagar', 'Driving car ', NULL, 0, '2024-06-03 04:38:40', '2024-06-03 04:42:00', 1),
(21, 13, '469863.jpg', NULL, 12, '5', 35, 'Kanpur nagar', 'Driving car ', '', 1, '2024-06-03 04:41:12', '2024-06-03 04:43:23', 1),
(23, 16, '645224.jpg', NULL, 14, '5', 35, 'Kanpur nagar', 'All things -facial, threading, manicure, pedicure, wax massage', '', 1, '2024-06-03 06:56:45', '2024-06-03 07:09:29', 1),
(24, 17, '45328.jpg', NULL, 20, '10', 35, 'Kanpur nagar', 'Ladies and gentlemen tailor', NULL, 0, '2024-06-03 07:13:28', '2024-06-03 07:15:52', 1),
(25, 18, '454574.jpg', NULL, 3, '7', 35, 'Kanpur nagar', 'Home and commercial site painter', '', 1, '2024-06-03 07:18:53', '2024-06-03 07:27:38', 1),
(26, 14, '912971.jpg', NULL, 26, '15', 35, 'Kanpur nagar', 'All kind of carpentry work', '', 1, '2024-06-03 07:25:17', '2024-06-03 07:27:34', 1),
(27, 19, NULL, NULL, 12, NULL, NULL, NULL, NULL, NULL, 0, '2024-06-03 07:40:50', '2024-06-03 07:40:50', 1),
(28, 20, '988791.jpg', NULL, 7, '10', 35, 'Kanpur nagar', 'Change switch, meters,board, lights', '', 1, '2024-06-03 07:49:40', '2024-06-03 07:52:22', 1),
(29, 21, '529549.jpg', NULL, 6, '15', 35, 'Kanpur nagar', 'Plumber all kind of work', '', 1, '2024-06-04 03:48:09', '2024-06-04 03:49:22', 1),
(30, 22, '557285.jpg', NULL, 17, '10', 35, 'Kanpur nagar', 'Gardner ', '', 1, '2024-06-04 04:13:08', '2024-06-04 05:07:56', 1),
(31, 23, '929614.jpg', NULL, 12, '10', 35, 'Kanpur nagar', 'Driver ', '', 1, '2024-06-04 05:04:40', '2024-06-04 05:07:42', 1),
(32, 24, '895910.jpg', NULL, 22, '10', 35, 'Kalyanpur, chaubepur, Kanpur dehat', 'RO mechanic', '', 1, '2024-06-04 07:14:13', '2024-06-04 07:22:39', 1),
(33, 25, '393641.jpg', NULL, 22, '10', 35, 'Kalyanpur, chaubepur, Kanpur dehat', 'RO mechanic', '', 1, '2024-06-04 07:19:10', '2024-06-04 07:22:15', 1),
(34, 26, '777740.jpg', NULL, 23, '35', 35, 'Rawatpur,kakadev ,Kanpur nagar', 'Bhagwat Katha vachak, kundali, horoscope', '', 1, '2024-06-04 07:30:57', '2024-06-04 07:49:56', 1),
(35, 27, '638425.jpg', NULL, 7, '10', 35, 'Rawatpur, Kanpur nagar', 'Electrician, wiring, switch, socket ', '', 1, '2024-06-04 07:36:30', '2024-06-04 07:49:50', 1),
(36, 28, '218658.jpg', NULL, 21, '5', 35, 'Rawatpur,kakadev,Kanpur nagar', 'Bike mechanic', '', 1, '2024-06-04 07:43:27', '2024-06-04 07:47:54', 1),
(37, 29, '546494.jpg', NULL, 27, '8', 35, 'Dada nagar,Kanpur nagar', 'Tool cutter, grinder, machine maintenance', '', 1, '2024-06-04 08:03:50', '2024-06-06 03:48:09', 1),
(38, 23, '184556.jpg', NULL, 27, '10', 35, 'Dada nagar, Kanpur nagar', 'Hydrolic, DG set, fitter maintenance, assembly', '', 1, '2024-06-04 08:09:36', '2024-06-06 03:48:17', 1),
(39, 30, '526958.jpg', NULL, 9, '15', 35, 'Rawatpur, kakadev, swaroop nagar, Kanpur nagar', 'TV , inverter mechanic', NULL, 0, '2024-06-04 15:48:25', '2024-06-04 15:58:08', 1),
(40, 30, '916851.jpg', NULL, 24, '15', 35, 'Rawatpur,kakadev , swaroop nagar,Kanpur nagar', 'Washing machine mechanic', NULL, 0, '2024-06-04 15:53:05', '2024-06-04 15:55:15', 1),
(41, 30, '916851.jpg', NULL, 24, '15', 35, 'Rawatpur,kakadev , swaroop nagar,Kanpur nagar', 'Washing machine mechanic', '', 1, '2024-06-04 15:55:04', '2024-06-04 16:06:21', 1),
(42, 30, '526958.jpg', NULL, 9, '15', 35, 'Rawatpur, kakadev, swaroop nagar, Kanpur nagar', 'TV , inverter mechanic', '', 1, '2024-06-04 15:57:13', '2024-06-04 16:05:24', 1),
(43, 31, '394925.jpg', NULL, 7, '10', 35, 'Rawatpur,kakadev Kanpur nagar', 'Electrician , All kind of electric repairing', '', 1, '2024-06-04 16:01:06', '2024-06-04 16:05:17', 1),
(44, 32, '631660.jpg', NULL, 15, '5', 35, 'Kanpur nagar', 'Construction of House, Building', '', 1, '2024-06-06 03:36:56', '2024-06-06 03:44:25', 1),
(45, 33, '880372.jpg', NULL, 6, '5', 35, 'Kanpur nagar', 'Plumber ', '', 1, '2024-06-07 03:53:12', '2024-06-07 03:58:27', 1),
(46, 34, '909599.jpg', NULL, 3, '8', 35, 'Kanpur nagar', 'Painter', '', 1, '2024-06-07 05:53:41', '2024-06-07 05:55:16', 1),
(52, 36, '285505.jpg', NULL, 7, '5', 35, 'Kanpur', 'Electrician ', '', 1, '2024-06-10 18:43:17', '2024-06-10 18:45:07', 1),
(53, 37, '736335.jpg', NULL, 14, '10', 35, 'Kanpur', 'Beautician', '', 1, '2024-06-11 12:37:23', '2024-06-11 12:45:39', 1),
(59, 35, '879217.jpg', '915331.jpg', 1, '5', 16, 'Jamshedpur', 'Ac technicians', '', 1, '2024-06-12 04:54:00', '2024-06-12 05:08:44', 0),
(61, 39, '808713.jpg', NULL, 14, '15', 35, 'Kanpur', 'Beauty parlour ', NULL, 0, '2024-08-13 12:26:54', '2024-08-13 12:28:16', 1),
(62, 40, '664257.jpeg', NULL, 13, '5', 35, 'Kanpur', 'Hair cutting ', NULL, 0, '2024-08-13 12:48:35', '2024-08-13 12:49:25', 1),
(63, 41, '932421.jpg', NULL, 23, '30', 35, 'Kanpur', 'Kundali, hawan ,Puja ,shadi ', '', 1, '2024-08-14 08:48:10', '2024-08-14 09:05:07', 1),
(66, 12, '441221.jpg', NULL, 30, '5', 35, 'Kanpur', 'â‚¹500 for 8 hours a Day', '', 1, '2024-08-28 18:59:05', '2025-06-01 14:54:03', 0),
(67, 12, '931597.jpg', NULL, 28, '5', 35, 'Kanpur', 'â‚¹600 for 12 hour a day', '', 1, '2024-08-28 19:10:50', '2024-08-28 19:13:19', 1),
(69, 12, '160210.jpg', NULL, 11, '10', 35, 'Kanpur', 'Toilet cleaner| â‚¹300 for 3 hour', '', 1, '2024-08-28 19:21:14', '2024-08-28 20:21:46', 1),
(70, 12, '936808.jpg', NULL, 7, '10', 35, 'Kanpur', 'â‚¹500 for 4 hour ', '', 1, '2024-08-28 19:42:33', '2024-08-28 20:21:50', 1),
(74, 46, '747510.jpg', NULL, 7, '5', 35, 'Kanpur,Kanpur Dehat', 'ITI in electrical ', '', 1, '2024-09-08 09:32:54', '2024-09-08 09:47:31', 1),
(75, 47, '396791.jpg', NULL, 6, '5', 35, 'Kanpur', 'Highly skilled in plumbing fixes ', '', 1, '2024-09-08 09:43:13', '2024-09-08 09:47:26', 1),
(76, 48, '528356.jpg', NULL, 12, '10', 35, 'Kanpur', 'Tractor,truck car Driver ', '', 1, '2024-10-04 09:36:10', '2024-10-04 09:38:37', 1),
(79, 52, '205995.jpg', NULL, 1, '5', 35, 'Kanpur,Kanpur Dehat', 'Complete AC mechanic Rs. 250 for 3 hours ', '', 1, '2024-10-10 07:16:57', '2024-10-10 07:22:06', 1),
(83, 12, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-01 13:47:04', '2025-06-01 13:47:04', 1),
(84, 12, NULL, NULL, 6, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-01 13:48:17', '2025-06-01 13:48:17', 1),
(85, 12, NULL, NULL, 6, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-01 13:50:06', '2025-06-01 13:50:06', 1),
(86, 12, NULL, NULL, 6, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-01 14:04:10', '2025-06-01 14:04:10', 1),
(87, 12, NULL, NULL, 6, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-01 14:05:05', '2025-06-01 14:05:05', 1),
(88, 12, NULL, NULL, 6, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-01 14:55:09', '2025-06-01 14:55:09', 1);

-- --------------------------------------------------------

--
-- Table structure for table `tabl_worker_ratings`
--

DROP TABLE IF EXISTS `tabl_worker_ratings`;
CREATE TABLE IF NOT EXISTS `tabl_worker_ratings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `worker_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `rating` int DEFAULT NULL,
  `comment` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Table structure for table `tabl_worker_services`
--

DROP TABLE IF EXISTS `tabl_worker_services`;
CREATE TABLE IF NOT EXISTS `tabl_worker_services` (
  `id` int NOT NULL AUTO_INCREMENT,
  `worker_id` int DEFAULT NULL,
  `service_type_id` int DEFAULT NULL,
  `service_name` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `price` int DEFAULT NULL,
  `provider_name` varchar(255) DEFAULT NULL,
  `servicesInclude` varchar(255) DEFAULT NULL,
  `status` tinyint DEFAULT '0',
  `date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=57 DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tabl_worker_services`
--

INSERT INTO `tabl_worker_services` (`id`, `worker_id`, `service_type_id`, `service_name`, `image`, `price`, `provider_name`, `servicesInclude`, `status`, `date`) VALUES
(19, 40, 8, 'Hair cut ', '602652.jpg', 500, 'Imran', 'All cuts', 1, '2024-08-13 12:52:59'),
(24, 39, 4, 'Skin care ', '871543.png', 900, 'Minno verma ', 'D tan clean up ', 1, '2024-08-23 09:24:38'),
(25, 39, 4, 'Skin care ', '706891.png', 1200, 'Minno verma ', 'Nourishing clean up ( OÂ³ + ) ', 1, '2024-08-23 09:30:31'),
(29, 39, 4, 'Skin care ', '557606.png', 1700, 'Minno verma ', 'D- tan facial ', 0, '2024-08-26 11:06:29'),
(28, 39, 4, 'Skin care ', '218779.png', 1700, 'Minno verma ', 'D tan facial ', 1, '2024-08-23 09:40:20'),
(15, 38, 4, 'Spa ', '676460.jpeg', 2000, 'Asha', 'Hair, manicure and pedicure ', 1, '2024-08-07 17:39:19'),
(16, 12, 4, 'Hair cutting ', '50622.jpg', 150, 'Riya', 'Butterfly cut , straight cut etc', 1, '2024-08-10 12:43:58'),
(26, 39, 4, 'Skin care ', '378236.png', 1600, 'Minno verma ', 'Derma bright clean up ', 1, '2024-08-23 09:36:31'),
(27, 39, 4, 'Skin care ', '27560.png', 2000, 'Minno verma ', 'Hydra boost ( OÂ³+ )', 1, '2024-08-23 09:38:49'),
(30, 39, 4, 'Skin care ', '435648.png', 2200, 'Minno verma ', 'Whitening facial ', 1, '2024-08-26 11:07:51'),
(31, 39, 4, 'Skin care ', '912964.png', 2700, 'Minno verma ', 'Nourishing facial ', 1, '2024-08-26 11:09:07'),
(32, 39, 4, 'Skin care ', '39024.png', 2500, 'Minno verma ', 'Seaweed facial ( acne skin )', 1, '2024-08-26 11:22:04'),
(33, 39, 4, 'Skin care ', '970295.png', 3500, 'Minno verma ', 'Vitamin c ( OÂ³ , lotus, kampeki ,all brand available )', 1, '2024-08-26 11:30:10'),
(34, 39, 4, 'Waxing ', '901936.png', 800, 'Minno verma ', 'Full arms and under arms ( rica , gel wax ) ', 1, '2024-08-26 12:00:34'),
(35, 39, 4, 'Waxing ', '252415.jpg', 750, 'Minno verma', 'Full legs ( rica , gel wax ) ', 1, '2024-08-26 12:02:39'),
(36, 39, 4, 'Waxing ', '650050.png', 4000, 'Prerna ', 'Full body ( rica , gel wax ) ', 1, '2024-08-26 12:06:14'),
(37, 39, 4, 'Face waxing ', '480102.png', 250, 'Pre', 'Side lock ', 1, '2024-08-26 12:09:56'),
(38, 39, 4, 'Face waxing ', '494788.png', 200, 'Prerna ', 'Forehead, upper lip ( peel off wax ) ', 1, '2024-08-26 12:11:16'),
(39, 39, 4, 'Face waxing ', '575839.png', 600, 'Prerna ', 'Full face ', 1, '2024-08-26 12:12:30'),
(40, 39, 4, 'Pedicure ', '85907.png', 600, 'Minno verma ', 'Regular pedicure ', 1, '2024-08-26 12:16:51'),
(41, 39, 4, 'Pedicure ', '479025.png', 800, 'Minno verma ', 'Paraffin pedicure ', 1, '2024-08-26 12:17:41'),
(42, 39, 4, 'Pedicure ', '903308.png', 1300, 'Minno verma ', 'Crystal crush pedicure ', 1, '2024-08-26 12:18:34'),
(43, 39, 4, 'Pedicure ', '173275.png', 1000, 'Minno verma ', 'Mint o cool pedicure ', 1, '2024-08-26 12:20:20'),
(44, 39, 4, 'Skin care ', '277250.png', 3000, 'Minno verma ', 'Full body D _ tan ', 1, '2024-08-26 12:25:55'),
(45, 39, 4, 'Skin care ', '16847.png', 2000, 'Minno verma ', 'Full body d tan ', 1, '2024-08-26 12:27:39'),
(46, 39, 4, 'Face care ', '502333.png', 120, 'Minno verma ', 'Eyebrow , forehead , upper lips ', 1, '2024-08-26 12:35:20'),
(47, 42, 10, 'Scooty for Girls ', '262002.png', 3000, 'Garima Gupta ', '3000 for 10 days | 1 hour each| training, including pick &Drop e', 1, '2024-08-26 12:38:29'),
(48, 43, 10, 'Car Driving ', '852641.jpg', 2500, 'Ps motor ', '15 days training| 5 km each day| pick& drop , petrol include', 1, '2024-08-27 15:48:35'),
(49, 39, 4, 'Hand care ', '765837.png', 500, 'Minno verma ', 'Regular manicure ', 1, '2024-08-28 09:35:43'),
(50, 39, 4, 'Hand care ', '527244.png', 600, 'Minno verma', 'Paraffin manicure ', 1, '2024-08-28 09:36:52'),
(51, 39, 4, 'Hand care ', '234067.png', 800, 'Minno verma ', 'Mint o cool manicure ', 1, '2024-08-28 09:37:44'),
(52, 39, 4, 'Hand care ', '106387.png', 1200, 'Minno verma ', 'Crystal crush manicure ', 1, '2024-08-28 09:38:24'),
(53, 39, 4, 'Package ', '992190.png', 2000, 'Minno verma ', 'Clean up , manicure , pedicure , eyebrow, forehead , upper lips full arms wax with under arms ', 1, '2024-08-28 09:55:05'),
(54, 39, 4, 'Package ', '48744.png', 1500, 'Minno verma ', 'Hair spa ,  full arms -full legs  waxing , eyebrows, forehead, upper lips ', 1, '2024-08-28 10:00:45'),
(55, 39, 4, 'Package ', '476016.png', 2500, 'Minno verma ', 'Root touchup , clean up , hair spa , Full arms waxing , eyebrow ', 1, '2024-08-28 10:26:20'),
(56, 44, 11, 'Yoga,bodybuilding, armwrestling ', '74310.jpg', 150, 'Mridul Katiyar', 'Yoga training,bodybuilding training,weightloss', 0, '2024-09-02 17:36:31');
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
