<?php
require_once('admin/lib/db_connection.php');
require_once('lib/auth.php');

$user_id = $_SESSION['user_id'];
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bulk Booking Request - GRS Worker</title>
    <link rel="stylesheet" href="assets/css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baloo+Bhai+2:wght@400..800&display=swap" rel="stylesheet">
</head>

<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: "Baloo Bhai 2";
        min-height: 100vh;
    }

    a {
        text-decoration: none;
    }

    /* Modern Page Header */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        text-align: center;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
    }

    /* Form Section */
    .form-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e9ecef;
    }

    .section-title i {
        color: #667eea;
        font-size: 1.2rem;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-label .required {
        color: #e74c3c;
        margin-left: 3px;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-submit {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem 3rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
    }

    .info-box {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border: 1px solid #667eea;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .info-box h6 {
        color: #667eea;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .info-box ul {
        margin-bottom: 0;
        padding-left: 1.2rem;
    }

    .info-box li {
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .form-section {
            padding: 1.5rem;
        }
        
        .btn-submit {
            padding: 0.875rem 2rem;
            font-size: 1rem;
        }
    }

    @media (max-width: 480px) {
        .page-header {
            padding: 2rem 0;
        }
        
        .page-title {
            font-size: 1.5rem;
        }
        
        .form-section {
            padding: 1rem;
        }
    }
</style>

<body>
    <!-- header -->
    <?php require_once('inc/header.php'); ?>
    <!-- /header -->

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fa-solid fa-users-cog"></i>
                Bulk Booking Request
            </h1>
            <p class="page-subtitle">Request multiple workers for your project - Get custom quotes</p>
        </div>
    </div>

    <div class="container pb-5">
        <!-- Information Box -->
        <div class="info-box">
            <h6><i class="fa-solid fa-info-circle"></i> How Bulk Booking Works:</h6>
            <ul>
                <li><strong>Submit Request:</strong> Fill out the detailed form with your project requirements</li>
                <li><strong>Get Custom Quote:</strong> Our team will review and provide a tailored quote within 24-48 hours</li>
                <li><strong>Worker Assignment:</strong> We'll assign qualified workers based on your specifications</li>
                <li><strong>Project Management:</strong> Track progress and manage your bulk booking through your dashboard</li>
                <li><strong>Quality Assurance:</strong> All workers are verified and monitored for quality service delivery</li>
            </ul>
        </div>

        <!-- Bulk Booking Form -->
        <form id="bulkBookingForm" method="POST" action="ajax/create_bulk_booking.php">
            <!-- Project Details Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fa-solid fa-clipboard-list"></i>
                    Project Details
                </h3>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="cat_id" class="form-label">
                            <i class="fa-solid fa-th-large"></i> Service Category <span class="required">*</span>
                        </label>
                        <select class="form-select" id="cat_id" name="cat_id" required>
                            <option value="">Select Service Category</option>
                            <?php
                            $catQuery = dbQuery("SELECT cat_id, cat_name FROM tabl_categories WHERE cat_status=1 ORDER BY cat_name");
                            while ($cat = dbFetchAssoc($catQuery)) {
                                echo "<option value='{$cat['cat_id']}'>{$cat['cat_name']}</option>";
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="work_title" class="form-label">
                            <i class="fa-solid fa-heading"></i> Project Title <span class="required">*</span>
                        </label>
                        <input type="text" class="form-control" id="work_title" name="work_title" 
                               placeholder="Brief title for your project" required>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="work_description" class="form-label">
                        <i class="fa-solid fa-file-text"></i> Detailed Work Description <span class="required">*</span>
                    </label>
                    <textarea class="form-control" id="work_description" name="work_description" rows="4" 
                              placeholder="Describe your project requirements in detail..." required></textarea>
                </div>
            </div>

            <!-- Worker Requirements Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fa-solid fa-users"></i>
                    Worker Requirements
                </h3>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="required_workers" class="form-label">
                            <i class="fa-solid fa-user-plus"></i> Number of Workers <span class="required">*</span>
                        </label>
                        <input type="number" class="form-control" id="required_workers" name="required_workers"
                               min="1" max="100" value="1" required>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="budget_per_worker" class="form-label">
                            <i class="fa-solid fa-rupee-sign"></i> Budget per Worker (₹)
                        </label>
                        <input type="number" class="form-control" id="budget_per_worker" name="budget_per_worker"
                               min="0" step="0.01" placeholder="Optional">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="total_budget" class="form-label">
                            <i class="fa-solid fa-calculator"></i> Total Project Budget (₹)
                        </label>
                        <input type="number" class="form-control" id="total_budget" name="total_budget"
                               min="0" step="0.01" placeholder="Optional">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="required_experience" class="form-label">
                            <i class="fa-solid fa-medal"></i> Required Experience Level
                        </label>
                        <select class="form-select" id="required_experience" name="required_experience">
                            <option value="">Any Experience Level</option>
                            <option value="fresher">Fresher (0-1 years)</option>
                            <option value="beginner">Beginner (1-2 years)</option>
                            <option value="intermediate">Intermediate (2-5 years)</option>
                            <option value="experienced">Experienced (5-10 years)</option>
                            <option value="expert">Expert (10+ years)</option>
                        </select>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="work_duration" class="form-label">
                            <i class="fa-solid fa-clock"></i> Expected Work Duration
                        </label>
                        <input type="text" class="form-control" id="work_duration" name="work_duration"
                               placeholder="e.g., 2 weeks, 1 month, 3 days">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="start_date" class="form-label">
                            <i class="fa-solid fa-calendar-start"></i> Preferred Start Date
                        </label>
                        <input type="date" class="form-control" id="start_date" name="start_date">
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="end_date" class="form-label">
                            <i class="fa-solid fa-calendar-end"></i> Expected End Date
                        </label>
                        <input type="date" class="form-control" id="end_date" name="end_date">
                    </div>
                </div>
            </div>

            <!-- Location Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fa-solid fa-map-marker-alt"></i>
                    Location Information
                </h3>

                <div class="mb-3">
                    <label for="location_type" class="form-label">
                        <i class="fa-solid fa-location-dot"></i> Work Location Type <span class="required">*</span>
                    </label>
                    <select class="form-select" id="location_type" name="location_type" required>
                        <option value="single">Single Location</option>
                        <option value="multiple">Multiple Locations</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="primary_address" class="form-label">
                        <i class="fa-solid fa-home"></i> Primary Work Address <span class="required">*</span>
                    </label>
                    <textarea class="form-control" id="primary_address" name="primary_address" rows="3"
                              placeholder="Enter complete address with landmarks" required></textarea>
                </div>

                <div class="mb-3" id="additional_addresses_div" style="display: none;">
                    <label for="additional_addresses" class="form-label">
                        <i class="fa-solid fa-map"></i> Additional Work Addresses
                    </label>
                    <textarea class="form-control" id="additional_addresses" name="additional_addresses" rows="3"
                              placeholder="Enter additional addresses (one per line)"></textarea>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="city" class="form-label">
                            <i class="fa-solid fa-city"></i> City <span class="required">*</span>
                        </label>
                        <input type="text" class="form-control" id="city" name="city"
                               placeholder="Enter city name" required>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="state" class="form-label">
                            <i class="fa-solid fa-flag"></i> State <span class="required">*</span>
                        </label>
                        <select class="form-select" id="state" name="state" required>
                            <option value="">Select State</option>
                            <?php
                            $stateQuery = dbQuery("SELECT state_id, state_name FROM tabl_states ORDER BY state_name");
                            while ($state = dbFetchAssoc($stateQuery)) {
                                echo "<option value='{$state['state_id']}'>{$state['state_name']}</option>";
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="pincode" class="form-label">
                            <i class="fa-solid fa-map-pin"></i> Pincode <span class="required">*</span>
                        </label>
                        <input type="text" class="form-control" id="pincode" name="pincode"
                               placeholder="Enter pincode" pattern="[0-9]{6}" maxlength="6" required>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fa-solid fa-address-book"></i>
                    Contact Information
                </h3>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_person" class="form-label">
                            <i class="fa-solid fa-user"></i> Contact Person Name <span class="required">*</span>
                        </label>
                        <input type="text" class="form-control" id="contact_person" name="contact_person"
                               placeholder="Full name of contact person" required>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="contact_phone" class="form-label">
                            <i class="fa-solid fa-phone"></i> Primary Phone Number <span class="required">*</span>
                        </label>
                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                               placeholder="Enter primary contact number" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_email" class="form-label">
                            <i class="fa-solid fa-envelope"></i> Email Address
                        </label>
                        <input type="email" class="form-control" id="contact_email" name="contact_email"
                               placeholder="Enter email address (optional)">
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="alternative_phone" class="form-label">
                            <i class="fa-solid fa-phone-alt"></i> Alternative Phone Number
                        </label>
                        <input type="tel" class="form-control" id="alternative_phone" name="alternative_phone"
                               placeholder="Enter alternative contact number">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="preferred_contact_time" class="form-label">
                            <i class="fa-solid fa-clock"></i> Preferred Contact Time
                        </label>
                        <select class="form-select" id="preferred_contact_time" name="preferred_contact_time">
                            <option value="">Any Time</option>
                            <option value="morning">Morning (9 AM - 12 PM)</option>
                            <option value="afternoon">Afternoon (12 PM - 5 PM)</option>
                            <option value="evening">Evening (5 PM - 8 PM)</option>
                            <option value="business_hours">Business Hours (9 AM - 6 PM)</option>
                        </select>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="urgency_level" class="form-label">
                            <i class="fa-solid fa-exclamation-triangle"></i> Urgency Level
                        </label>
                        <select class="form-select" id="urgency_level" name="urgency_level">
                            <option value="low">Low - Can wait 1-2 weeks</option>
                            <option value="medium" selected>Medium - Need within a week</option>
                            <option value="high">High - Need within 2-3 days</option>
                            <option value="urgent">Urgent - Need immediately</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Additional Requirements Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fa-solid fa-cogs"></i>
                    Additional Requirements
                </h3>

                <div class="mb-3">
                    <label for="special_requirements" class="form-label">
                        <i class="fa-solid fa-list-ul"></i> Special Requirements or Notes
                    </label>
                    <textarea class="form-control" id="special_requirements" name="special_requirements" rows="4"
                              placeholder="Any special requirements, tools needed, safety considerations, or additional notes..."></textarea>
                </div>
            </div>

            <!-- Submit Section -->
            <div class="form-section">
                <div class="text-center">
                    <div class="alert alert-warning mb-4">
                        <h6><i class="fa-solid fa-info-circle"></i> Important Note:</h6>
                        <p class="mb-0">
                            This is a quotation request. Our team will review your requirements and provide a detailed quote within 24-48 hours.
                            No payment is required at this stage.
                        </p>
                    </div>

                    <div class="d-flex gap-3 justify-content-center">
                        <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                            <i class="fa-solid fa-arrow-left"></i> Go Back
                        </button>
                        <button type="submit" class="btn btn-submit">
                            <i class="fa-solid fa-paper-plane"></i> Submit Bulk Booking Request
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- footer -->
    <?php require_once('inc/footer.php'); ?>
    <!-- /footer -->

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
        integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"
        integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy"
        crossorigin="anonymous"></script>

    <script>
        $(document).ready(function() {
            // Set minimum dates to today
            const today = new Date().toISOString().split('T')[0];
            $('#start_date').attr('min', today);
            $('#end_date').attr('min', today);

            // Show/hide additional addresses based on location type
            $('#location_type').change(function() {
                if ($(this).val() === 'multiple') {
                    $('#additional_addresses_div').show();
                } else {
                    $('#additional_addresses_div').hide();
                    $('#additional_addresses').val('');
                }
            });

            // Auto-calculate total budget when budget per worker changes
            $('#budget_per_worker, #required_workers').on('input', function() {
                const budgetPerWorker = parseFloat($('#budget_per_worker').val()) || 0;
                const requiredWorkers = parseInt($('#required_workers').val()) || 0;

                if (budgetPerWorker > 0 && requiredWorkers > 0) {
                    const totalBudget = budgetPerWorker * requiredWorkers;
                    $('#total_budget').val(totalBudget.toFixed(2));
                }
            });

            // Validate end date is after start date
            $('#start_date, #end_date').change(function() {
                const startDate = $('#start_date').val();
                const endDate = $('#end_date').val();

                if (startDate && endDate && endDate < startDate) {
                    alert('End date cannot be before start date');
                    $('#end_date').val('');
                }
            });

            // Form submission
            $('#bulkBookingForm').submit(function(e) {
                e.preventDefault();

                const submitBtn = $('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fa-solid fa-spinner fa-spin"></i> Submitting...').prop('disabled', true);

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: $(this).serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            alert('Success! ' + response.message);
                            window.location.href = 'bulk_bookings.php';
                        } else {
                            alert('Error: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('An error occurred. Please try again.');
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });
    </script>
</body>

</html>
