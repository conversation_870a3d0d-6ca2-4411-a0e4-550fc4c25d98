<?php
require_once('admin/lib/db_connection.php');
require_once('lib/auth.php');

$user_id = $_SESSION['user_id'];

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$page = $_GET['page'] ?? 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Build where clause
$whereConditions = ["user_id = '$user_id'"];
if (!empty($status_filter)) {
    $whereConditions[] = "status = '$status_filter'";
}
$whereClause = implode(' AND ', $whereConditions);

// Get bulk bookings
$query = "SELECT bb.*, c.cat_name, c.cat_icon
          FROM tabl_bulk_booking bb
          LEFT JOIN tabl_categories c ON bb.cat_id = c.cat_id
          WHERE $whereClause
          ORDER BY bb.created_at DESC
          LIMIT $limit OFFSET $offset";

$result = dbQuery($query);

// Count total results
$countQuery = "SELECT COUNT(*) as total FROM tabl_bulk_booking WHERE $whereClause";
$countResult = dbQuery($countQuery);
$totalResults = dbFetchAssoc($countResult)['total'];
$totalPages = ceil($totalResults / $limit);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Bulk Bookings - GRS Worker</title>
    <link rel="stylesheet" href="assets/css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baloo+Bhai+2:wght@400..800&display=swap" rel="stylesheet">
</head>

<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: "Baloo Bhai 2";
        min-height: 100vh;
    }

    a {
        text-decoration: none;
    }

    /* Modern Page Header */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        text-align: center;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    /* Booking Cards */
    .booking-card {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        position: relative;
        border: 2px solid transparent;
        margin-bottom: 2rem;
    }

    .booking-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 50px rgba(0,0,0,0.2);
        border-color: #667eea;
    }

    .booking-header {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
    }

    .booking-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .booking-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .booking-category {
        color: #667eea;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .status-0 { background: #fff3cd; color: #856404; }
    .status-1 { background: #d1ecf1; color: #0c5460; }
    .status-2 { background: #d4edda; color: #155724; }
    .status-3 { background: #f8d7da; color: #721c24; }
    .status-4 { background: #cce5ff; color: #004085; }
    .status-5 { background: #d1e7dd; color: #0f5132; }
    .status-6 { background: #f8d7da; color: #842029; }

    .booking-body {
        padding: 1.5rem;
    }

    .booking-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .detail-group h6 {
        color: #667eea;
        font-weight: 700;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .detail-item {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .detail-label {
        font-weight: 600;
        color: #2c3e50;
    }

    .detail-value {
        color: #7f8c8d;
    }

    .booking-actions {
        border-top: 1px solid #e9ecef;
        padding: 1rem 1.5rem;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .btn-view-details {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 8px 20px;
        border-radius: 20px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-view-details:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-new-request {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-new-request:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    /* Pagination */
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }

    .pagination {
        display: flex;
        gap: 0.5rem;
    }

    .page-link {
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .page-link:hover, .page-link.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    /* No Results */
    .no-results {
        text-align: center;
        padding: 3rem;
        color: #7f8c8d;
    }

    .no-results i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #bdc3c7;
    }

    .no-results h3 {
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .booking-meta {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .booking-details {
            grid-template-columns: 1fr;
        }
        
        .booking-actions {
            flex-direction: column;
            align-items: stretch;
        }
    }

    @media (max-width: 480px) {
        .page-header {
            padding: 2rem 0;
        }
        
        .page-title {
            font-size: 1.5rem;
        }
        
        .filter-section, .booking-card {
            margin: 0 1rem 2rem;
        }
    }
</style>

<body>
    <!-- header -->
    <?php require_once('inc/header.php'); ?>
    <!-- /header -->

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fa-solid fa-clipboard-list"></i>
                My Bulk Bookings
            </h1>
            <p class="page-subtitle">Track and manage your bulk booking requests</p>
        </div>
    </div>

    <div class="container">
        <!-- Filter and Actions -->
        <div class="filter-section">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <div class="d-flex align-items-center gap-3">
                    <label for="statusFilter" class="form-label mb-0 fw-bold">Filter by Status:</label>
                    <select class="form-select" id="statusFilter" style="width: auto;">
                        <option value="">All Status</option>
                        <option value="0" <?= $status_filter == '0' ? 'selected' : '' ?>>Pending</option>
                        <option value="1" <?= $status_filter == '1' ? 'selected' : '' ?>>Under Review</option>
                        <option value="2" <?= $status_filter == '2' ? 'selected' : '' ?>>Quoted</option>
                        <option value="3" <?= $status_filter == '3' ? 'selected' : '' ?>>Accepted</option>
                        <option value="4" <?= $status_filter == '4' ? 'selected' : '' ?>>In Progress</option>
                        <option value="5" <?= $status_filter == '5' ? 'selected' : '' ?>>Completed</option>
                        <option value="6" <?= $status_filter == '6' ? 'selected' : '' ?>>Cancelled</option>
                    </select>
                </div>
                
                <a href="bulk_booking.php" class="btn-new-request">
                    <i class="fa-solid fa-plus"></i> New Bulk Request
                </a>
            </div>
        </div>

        <!-- Bulk Bookings List -->
        <?php if ($totalResults > 0): ?>
            <?php while ($booking = dbFetchAssoc($result)):
                // Status labels and icons
                $statusLabels = [
                    0 => 'Pending Review',
                    1 => 'Under Review',
                    2 => 'Quote Provided',
                    3 => 'Quote Accepted',
                    4 => 'In Progress',
                    5 => 'Completed',
                    6 => 'Cancelled'
                ];
                $statusIcons = [
                    0 => 'clock',
                    1 => 'eye',
                    2 => 'file-invoice-dollar',
                    3 => 'handshake',
                    4 => 'cogs',
                    5 => 'check-circle',
                    6 => 'times-circle'
                ];

                // Format dates
                $createdDate = date('d/m/Y h:i A', strtotime($booking['created_at']));
                $startDate = $booking['start_date'] ? date('d/m/Y', strtotime($booking['start_date'])) : 'Not specified';
                $endDate = $booking['end_date'] ? date('d/m/Y', strtotime($booking['end_date'])) : 'Not specified';
            ?>
                <div class="booking-card">
                    <div class="booking-header">
                        <div class="booking-meta">
                            <div>
                                <h4 class="booking-title"><?= htmlspecialchars($booking['work_title']) ?></h4>
                                <div class="booking-category">
                                    <i class="fa-solid fa-briefcase"></i>
                                    <?= htmlspecialchars($booking['cat_name'] ?? 'Unknown Category') ?>
                                </div>
                            </div>
                            <div class="status-badge status-<?= $booking['status'] ?>">
                                <i class="fa-solid fa-<?= $statusIcons[$booking['status']] ?>"></i>
                                <?= $statusLabels[$booking['status']] ?>
                            </div>
                        </div>
                    </div>

                    <div class="booking-body">
                        <div class="booking-details">
                            <div class="detail-group">
                                <h6><i class="fa-solid fa-info-circle"></i> Project Details</h6>
                                <div class="detail-item">
                                    <span class="detail-label">Workers Needed:</span>
                                    <span class="detail-value"><?= $booking['required_workers'] ?></span>
                                </div>
                                <?php if ($booking['required_experience']): ?>
                                <div class="detail-item">
                                    <span class="detail-label">Experience:</span>
                                    <span class="detail-value"><?= ucfirst($booking['required_experience']) ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if ($booking['work_duration']): ?>
                                <div class="detail-item">
                                    <span class="detail-label">Duration:</span>
                                    <span class="detail-value"><?= htmlspecialchars($booking['work_duration']) ?></span>
                                </div>
                                <?php endif; ?>
                                <div class="detail-item">
                                    <span class="detail-label">Urgency:</span>
                                    <span class="detail-value"><?= ucfirst($booking['urgency_level']) ?></span>
                                </div>
                            </div>

                            <div class="detail-group">
                                <h6><i class="fa-solid fa-calendar"></i> Timeline</h6>
                                <div class="detail-item">
                                    <span class="detail-label">Requested:</span>
                                    <span class="detail-value"><?= $createdDate ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Start Date:</span>
                                    <span class="detail-value"><?= $startDate ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">End Date:</span>
                                    <span class="detail-value"><?= $endDate ?></span>
                                </div>
                                <?php if ($booking['quote_date']): ?>
                                <div class="detail-item">
                                    <span class="detail-label">Quote Date:</span>
                                    <span class="detail-value"><?= date('d/m/Y h:i A', strtotime($booking['quote_date'])) ?></span>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="detail-group">
                                <h6><i class="fa-solid fa-map-marker-alt"></i> Location & Budget</h6>
                                <div class="detail-item">
                                    <span class="detail-label">City:</span>
                                    <span class="detail-value"><?= htmlspecialchars($booking['city']) ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Type:</span>
                                    <span class="detail-value"><?= ucfirst($booking['location_type']) ?> Location</span>
                                </div>
                                <?php if ($booking['budget_per_worker'] || $booking['total_budget']): ?>
                                <div class="detail-item">
                                    <span class="detail-label">Budget:</span>
                                    <span class="detail-value">
                                        <?php if ($booking['budget_per_worker']): ?>
                                            ₹<?= number_format($booking['budget_per_worker'], 0) ?>/worker
                                        <?php endif; ?>
                                        <?php if ($booking['total_budget']): ?>
                                            (Total: ₹<?= number_format($booking['total_budget'], 0) ?>)
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <?php endif; ?>
                                <?php if ($booking['quoted_amount']): ?>
                                <div class="detail-item">
                                    <span class="detail-label">Quoted Amount:</span>
                                    <span class="detail-value text-success fw-bold">₹<?= number_format($booking['quoted_amount'], 0) ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if ($booking['work_description']): ?>
                        <div class="alert alert-light">
                            <h6 class="mb-2"><i class="fa-solid fa-file-text"></i> Project Description</h6>
                            <p class="mb-0"><?= nl2br(htmlspecialchars(substr($booking['work_description'], 0, 200))) ?><?= strlen($booking['work_description']) > 200 ? '...' : '' ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if ($booking['quote_details']): ?>
                        <div class="alert alert-success">
                            <h6 class="mb-2"><i class="fa-solid fa-file-invoice-dollar"></i> Quote Details</h6>
                            <p class="mb-0"><?= nl2br(htmlspecialchars($booking['quote_details'])) ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if ($booking['admin_notes']): ?>
                        <div class="alert alert-info">
                            <h6 class="mb-2"><i class="fa-solid fa-sticky-note"></i> Admin Notes</h6>
                            <p class="mb-0"><?= nl2br(htmlspecialchars($booking['admin_notes'])) ?></p>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="booking-actions">
                        <div class="booking-id">
                            <small class="text-muted">Request ID: #<?= $booking['id'] ?></small>
                        </div>
                        <div>
                            <a href="bulk_booking_details.php?id=<?= $booking['id'] ?>" class="btn-view-details">
                                <i class="fa-solid fa-eye"></i> View Full Details
                            </a>
                        </div>
                    </div>
                </div>
            <?php endwhile; ?>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="pagination-wrapper">
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" class="page-link">
                            <i class="fa-solid fa-chevron-left"></i> Previous
                        </a>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                           class="page-link <?= $i == $page ? 'active' : '' ?>">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" class="page-link">
                            Next <i class="fa-solid fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

        <?php else: ?>
            <div class="no-results">
                <i class="fa-solid fa-clipboard-list"></i>
                <h3>No Bulk Booking Requests Found</h3>
                <p>You haven't submitted any bulk booking requests yet.</p>
                <a href="bulk_booking.php" class="btn-new-request mt-3">
                    <i class="fa-solid fa-plus"></i> Create Your First Bulk Request
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- footer -->
    <?php require_once('inc/footer.php'); ?>
    <!-- /footer -->

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
        integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"
        integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy"
        crossorigin="anonymous"></script>

    <script>
        $(document).ready(function() {
            // Status filter change
            $('#statusFilter').change(function() {
                const status = $(this).val();
                const url = new URL(window.location);
                if (status) {
                    url.searchParams.set('status', status);
                } else {
                    url.searchParams.delete('status');
                }
                url.searchParams.delete('page'); // Reset to first page
                window.location.href = url.toString();
            });
        });
    </script>
</body>

</html>
