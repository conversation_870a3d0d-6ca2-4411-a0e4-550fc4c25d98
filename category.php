<?php
require_once ('admin/lib/db_connection.php');
require_once ('lib/auth.php');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Service Categories - GRS Worker</title>
    <link rel="stylesheet" href="assets/css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baloo+Bhai+2:wght@400..800&display=swap" rel="stylesheet">
</head>
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: "Baloo Bhai 2";
        min-height: 100vh;
    }

    a {
        text-decoration: none;
    }

    /* Modern Page Header */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        text-align: center;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
    }

    /* Modern Category Section */
    .category-section-wrapper {
        padding: 2rem 0;
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .section-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .section-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: #667eea;
        font-size: 1.6rem;
    }

    .section-subtitle {
        color: #7f8c8d;
        font-size: 1rem;
        margin: 0;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        padding: 1rem 0;
        justify-items: center;
    }

    .category-card-modern {
        background: white;
        border-radius: 20px;
        padding: 2rem 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
        width: 100%;
        max-width: 220px;
        cursor: pointer;
        border: 3px solid transparent;
    }

    .category-card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .category-card-modern:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0,0,0,0.2);
        border-color: #667eea;
    }

    .category-card-modern.active {
        border-color: #667eea;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    }

    .category-link {
        text-decoration: none;
        color: inherit;
        display: block;
    }

    .category-icon-wrapper {
        position: relative;
        margin-bottom: 1.5rem;
        display: flex;
        justify-content: center;
    }

    .category-icon-container {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .category-icon {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 50%;
        transition: transform 0.3s ease;
    }

    .category-card-modern:hover .category-icon {
        transform: scale(1.1);
    }

    .worker-count-badge {
        position: absolute;
        top: -5px;
        right: 10px;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 4px 8px;
        font-size: 0.75rem;
        font-weight: 600;
        min-width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .category-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .category-workers {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Workers Grid Section */
    .workers-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .workers-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    /* Loader Styling */
    .loader {
        text-align: center;
        padding: 2rem;
    }

    .loader img {
        width: 50px;
        height: 50px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .categories-grid {
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 1.5rem;
        }

        .category-card-modern {
            padding: 1.5rem 1rem;
            max-width: 180px;
        }

        .category-icon-container {
            width: 60px;
            height: 60px;
        }

        .category-icon {
            width: 60px;
            height: 60px;
        }
    }

    @media (max-width: 480px) {
        .page-header {
            padding: 2rem 0;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .categories-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .category-card-modern {
            padding: 1rem 0.5rem;
            max-width: 150px;
        }

        .category-icon-container {
            width: 50px;
            height: 50px;
        }

        .category-icon {
            width: 50px;
            height: 50px;
        }

        .category-name {
            font-size: 0.95rem;
        }

        .category-workers {
            font-size: 0.8rem;
        }
    }
</style>

<body>
    <!-- header -->
    <?= require_once ('inc/header.php'); ?>
    <!-- /header -->

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fa-solid fa-th-large"></i>
                Service Categories
            </h1>
            <p class="page-subtitle">Choose from our wide range of professional services</p>
        </div>
    </div>

    <!-- Category Selection Section -->
    <div class="container">
        <div class="category-section-wrapper">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fa-solid fa-hand-pointer"></i>
                    Select a Category
                </h2>
                <p class="section-subtitle">Click on any category to view available workers</p>
            </div>

            <div class="categories-grid">
                <?php
                $catQuery = dbQuery("SELECT * FROM tabl_categories WHERE cat_status=1 ORDER BY cat_name ASC");
                if (dbNumRows($catQuery) > 0) {
                    while ($catData = dbFetchAssoc($catQuery)):
                        // Get worker count for this category
                        $candidateQuery = dbQuery("SELECT count(id) AS total FROM tabl_workers_category WHERE cat_id='" . $catData['cat_id'] . "' AND status='1'");
                        $candidates = dbFetchAssoc($candidateQuery);

                        // Fix image path
                        $imagePath = "admin/assets/img/category/thumb-50/" . $catData['cat_icon'];
                        if (!file_exists($imagePath)) {
                            $altPaths = [
                                "admin/assets/img/category/thumb-100/" . $catData['cat_icon'],
                                "admin/assets/img/category/" . $catData['cat_icon']
                            ];

                            $imagePath = null;
                            foreach ($altPaths as $altPath) {
                                if (file_exists($altPath)) {
                                    $imagePath = $altPath;
                                    break;
                                }
                            }
                        }
                ?>
                        <div class="category-card-modern catCard" data-id="<?= $catData['cat_id'] ?>">
                            <div class="category-icon-wrapper">
                                <div class="category-icon-container">
                                    <img class="category-icon"
                                         src="<?= $imagePath ?: 'assets/images/default-category.svg' ?>"
                                         alt="<?= $catData['cat_name'] ?>"
                                         onerror="this.src='assets/images/default-category.svg';">
                                </div>
                                <?php if ($candidates['total'] > 0): ?>
                                <div class="worker-count-badge">
                                    <span><?= $candidates['total'] ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="category-info">
                                <h5 class="category-name"><?= $catData['cat_name'] ?></h5>
                                <p class="category-workers"><?= $candidates['total'] ?> Workers Available</p>
                            </div>
                        </div>
                <?php
                    endwhile;
                } else {
                    echo '<div class="col-12 text-center">
                            <p class="text-muted">No categories available at the moment.</p>
                          </div>';
                }
                ?>
            </div>
        </div>
    </div>

    <!-- Workers Display Section -->
    <div class="container">
        <div class="workers-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fa-solid fa-users"></i>
                    Available Workers
                </h2>
                <p class="section-subtitle">Professional workers ready to serve you</p>
            </div>

            <div class="workers-grid" id="profileCardRow">
                <!-- Workers will be loaded here via AJAX -->
            </div>

            <div class="loader" id="loader1" style="display: none;">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">Loading workers...</span>
                </div>
            </div>
        </div>
    </div>
    <!-- footer -->
    <?php require_once ('inc/footer.php'); ?>
    <!-- /footer -->


    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
        integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"
        integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy"
        crossorigin="anonymous"></script>
    <script>

        function removeScrollBarPushing() {
            const offsetY = document.documentElement.scrollTop;
            let i = 0;
            const time = setInterval(function () {
                if (i++ < 2) {
                    clearInterval(time);
                }
                document.documentElement.scrollTop = offsetY;
            }, 1);
        }

        // open sidenav
        document
            .getElementById("nav-toggle-btn")
            .addEventListener("click", function () {
                document.getElementById("sidenav").classList.add("show");
                removeScrollBarPushing();
            });
        // close sidenav
        document
            .querySelector("#sidenav .closebtn")
            .addEventListener("click", function () {
                document.getElementById("sidenav").classList.remove("show");
            });

        $(".hamburger").on("click", function () {
            $(this).parent().toggleClass("active");
        });
        function toggleSearchInput() {
            var searchInput = document.querySelector(".search-input");
            searchInput.style.display =
                searchInput.style.display === "none" ||
                    searchInput.style.display === ""
                    ? "block"
                    : "none";
        }

        /* LOAD DATA ON PAGE REFERESH */
        let page = 1; // Initial page number
        let cat_id = 0
        function loadData(page, param = 0) {
            $('#loader1').show();
            cat_id = param;

            $.ajax({
                url: 'ajax/load_category_data.php',
                type: 'POST',
                data: {
                    page: page,
                    cat_id: param
                }, // Send the page number to the server
                success: function (response) {
                    $('#loader1').hide();
                    if (response.trim() === '0') {
                        // If response is '0', display "No data available" message and exit the function
                        if ($('#noDataMessage').length === 0) {
                            $('#profileCardRow').append('<div id="noDataMessage" class="col-lg-12 my-4"><p class="mt-4 text-center">No More data available! 😥</p></div>');
                        }
                        return;
                    }
                    try {
                        $('#noDataMessage').remove(); // Remove "No data available" message if it exists
                        $('#profileCardRow').append(response); // Append new data to the existing content
                    } catch (error) {
                        console.error("Error processing response:", error);
                    }
                },
                error: function (xhr, status, error) {
                    $('#loader1').hide();
                    console.error("Error fetching data:", xhr.responseText);
                }
            });

            page = page + 10;
        }

        $(document).ready(function () {
// geting id from url 
 const urlParams = new URLSearchParams(window.location.search);
      cat_id = urlParams.get('id');

        if (cat_id === null || cat_id === '') {
        cat_id = 0;
        }
           loadData(page,cat_id); // Initial data load

            $(window).scroll(function () {
                if ($(window).scrollTop() + $(window).height() == $(document).height()) {
                    page++; // Increment page number
                    loadData(page,cat_id); // Load more data when scrolled to the bottom
                }
            });


            $('.catCard').on('click', function () {
                let cat_id = $(this).attr('data-id');

                // Remove active class from all cards and add to clicked one
                $('.catCard').removeClass('active');
                $(this).addClass('active');

                // Clear previous results
                $('#noDataMessage').remove();
                $('#profileCardRow').empty();

                // Reset page counter
                page = 1;

                // Load new data
                loadData(1, cat_id);

                // Smooth scroll to workers section
                $('html, body').animate({
                    scrollTop: $('.workers-section').offset().top - 100
                }, 800);
            });
<?php
// if(isset($_GET['id']) && $_GET['id']!='')
// {
//     echo "loadData(1, ".$_GET['id'].");";
// }
// else
// {
//     echo " loadData(page); // Initial data load";
// }
?>
        });
    </script>
</body>

</html>