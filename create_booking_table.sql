-- Create new tabl_booking table for worker category bookings
-- This is separate from tabl_service_order and tabl_worker_bookings

DROP TABLE IF EXISTS `tabl_booking`;
CREATE TABLE IF NOT EXISTS `tabl_booking` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `worker_category_id` int DEFAULT NULL COMMENT 'References tabl_workers_category.id',
  `worker_id` int DEFAULT NULL COMMENT 'References tabl_workers.worker_id',
  `cat_id` int DEFAULT NULL COMMENT 'References tabl_categories.cat_id',
  `booking_date` date DEFAULT NULL,
  `booking_time` time DEFAULT NULL,
  `service_description` text COMMENT 'Additional requirements from user',
  `user_address` text NOT NULL COMMENT 'Service address',
  `user_phone` varchar(15) NOT NULL COMMENT 'Contact number',
  `total_price` decimal(10,2) DEFAULT NULL COMMENT 'Estimated price (can be updated by worker)',
  `status` tinyint(1) DEFAULT '0' COMMENT '0=pending, 1=confirmed, 2=completed, 3=cancelled',
  `worker_notes` text COMMENT 'Notes from worker',
  `completion_notes` text COMMENT 'Notes after service completion',
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_worker_id` (`worker_id`),
  KEY `idx_worker_category_id` (`worker_category_id`),
  KEY `idx_cat_id` (`cat_id`),
  KEY `idx_status` (`status`),
  KEY `idx_booking_date` (`booking_date`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3;

-- Add foreign key constraints (if using InnoDB)
-- ALTER TABLE `tabl_booking`
-- ADD CONSTRAINT `fk_booking_user` FOREIGN KEY (`user_id`) REFERENCES `tabl_users` (`user_id`),
-- ADD CONSTRAINT `fk_booking_worker` FOREIGN KEY (`worker_id`) REFERENCES `tabl_workers` (`worker_id`),
-- ADD CONSTRAINT `fk_booking_worker_category` FOREIGN KEY (`worker_category_id`) REFERENCES `tabl_workers_category` (`id`),
-- ADD CONSTRAINT `fk_booking_category` FOREIGN KEY (`cat_id`) REFERENCES `tabl_categories` (`cat_id`);

-- Insert sample data for testing (optional)
-- INSERT INTO `tabl_booking` (`user_id`, `worker_category_id`, `worker_id`, `cat_id`, `booking_date`, `booking_time`, `service_description`, `user_address`, `user_phone`, `total_price`, `status`) VALUES
-- (1, 16, 10, 18, '2024-12-25', '10:00:00', 'Need AC repair service', '123 Main Street, City', '9876543210', 500.00, 0);

-- Table created successfully
-- This table will store bookings for worker categories (different from service orders)
-- Each booking represents a user requesting service from a specific worker in a category
