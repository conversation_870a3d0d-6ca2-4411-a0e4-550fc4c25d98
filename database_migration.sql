-- Database Migration Script for Booking System Enhancement
-- Run this script to update your existing database

-- 1. Update tabl_service_order table to add new status fields
ALTER TABLE `tabl_service_order` 
MODIFY COLUMN `status` tinyint(1) DEFAULT '0' COMMENT '0=pending, 1=confirmed, 2=completed, 3=cancelled',
ADD COLUMN `booking_notes` text AFTER `status`,
ADD COLUMN `completion_notes` text AFTER `booking_notes`,
ADD COLUMN `completed_at` timestamp NULL DEFAULT NULL AFTER `completion_notes`;

-- 2. Create new tabl_worker_bookings table for direct worker profile bookings
CREATE TABLE IF NOT EXISTS `tabl_worker_bookings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `worker_category_id` int DEFAULT NULL,
  `worker_id` int DEFAULT NULL,
  `service_id` int DEFAULT NULL,
  `booking_date` date DEFAULT NULL,
  `booking_time` time DEFAULT NULL,
  `service_description` text,
  `user_address` text,
  `user_phone` varchar(15) DEFAULT NULL,
  `total_price` decimal(10,2) DEFAULT NULL,
  `status` tinyint(1) DEFAULT '0' COMMENT '0=pending, 1=confirmed, 2=completed, 3=cancelled',
  `worker_notes` text,
  `completion_notes` text,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3;

-- 3. Update tabl_worker_ratings table to link ratings with bookings
ALTER TABLE `tabl_worker_ratings` 
ADD COLUMN `booking_id` int DEFAULT NULL AFTER `user_id`;

-- 4. Update existing service orders to have proper status (optional - only if you want to reset all to pending)
-- UPDATE `tabl_service_order` SET `status` = 0 WHERE `status` = 1;

-- 5. Update existing tabl_worker_bookings table if it already exists
ALTER TABLE `tabl_worker_bookings`
ADD COLUMN `service_id` int DEFAULT NULL AFTER `worker_id`,
ADD COLUMN `total_price` decimal(10,2) DEFAULT NULL AFTER `user_phone`;

-- 6. Add indexes for better performance
ALTER TABLE `tabl_worker_bookings`
ADD INDEX `idx_user_id` (`user_id`),
ADD INDEX `idx_worker_id` (`worker_id`),
ADD INDEX `idx_worker_category_id` (`worker_category_id`),
ADD INDEX `idx_service_id` (`service_id`),
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_booking_date` (`booking_date`);

ALTER TABLE `tabl_worker_ratings`
ADD INDEX `idx_booking_id` (`booking_id`);

-- Migration completed successfully
-- The booking system is now ready with enhanced features:
-- 1. Direct worker profile bookings
-- 2. Improved booking status management
-- 3. Rating system linked to completed bookings
-- 4. Better booking tracking and management
