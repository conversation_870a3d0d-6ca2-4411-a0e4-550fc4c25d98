<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Functions Test</title>
    <!-- jQuery (load first) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Define JavaScript functions BEFORE HTML -->
    <script>
    // Global functions for service selection - defined immediately
    window.selectService = function(serviceId, serviceName, price) {
        console.log('selectService called with:', {serviceId, serviceName, price});
        
        // Check if jQuery is loaded
        if (typeof $ === 'undefined') {
            console.error('jQuery is not loaded!');
            alert('Error: Page not fully loaded. Please refresh and try again.');
            return;
        }
        
        try {
            alert('selectService function is working! Service: ' + serviceName + ', Price: ₹' + price);
            console.log('Service selection completed successfully');
        } catch (error) {
            console.error('Error in selectService:', error);
            alert('Error selecting service. Please try again.');
        }
    };

    // Global function for canceling booking
    window.cancelBooking = function() {
        console.log('cancelBooking called');
        
        // Check if jQuery is loaded
        if (typeof $ === 'undefined') {
            console.error('jQuery is not loaded!');
            alert('Error: Page not fully loaded. Please refresh and try again.');
            return;
        }
        
        try {
            alert('cancelBooking function is working!');
            console.log('Booking cancelled successfully');
        } catch (error) {
            console.error('Error in cancelBooking:', error);
        }
    };
    
    console.log('Global functions defined successfully');
    
    // Test function availability
    function testFunctions() {
        console.log('Testing function availability:');
        console.log('selectService exists:', typeof window.selectService === 'function');
        console.log('cancelBooking exists:', typeof window.cancelBooking === 'function');
        console.log('jQuery loaded:', typeof $ !== 'undefined');
        console.log('SweetAlert loaded:', typeof Swal !== 'undefined');
    }
    
    // Test immediately
    testFunctions();
    
    // Test again when DOM is ready
    $(document).ready(function() {
        console.log('DOM ready - testing functions again:');
        testFunctions();
    });
    </script>
</head>
<body>
    <h1>Function Debug Test</h1>
    
    <div style="padding: 20px;">
        <h2>Test Buttons</h2>
        
        <p>Click these buttons to test if the functions are working:</p>
        
        <button type="button" onclick="selectService(1, 'Test Service', 100)" style="padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px;">
            Test selectService Function
        </button>
        
        <button type="button" onclick="cancelBooking()" style="padding: 10px 20px; margin: 10px; background: #dc3545; color: white; border: none; border-radius: 5px;">
            Test cancelBooking Function
        </button>
        
        <button type="button" onclick="testFunctions()" style="padding: 10px 20px; margin: 10px; background: #28a745; color: white; border: none; border-radius: 5px;">
            Test Function Availability
        </button>
        
        <h3>Console Output</h3>
        <p>Open browser console (F12) to see detailed test results.</p>
        
        <h3>Expected Results</h3>
        <ul>
            <li>✅ All buttons should work without errors</li>
            <li>✅ Console should show "Global functions defined successfully"</li>
            <li>✅ Console should show all functions exist</li>
            <li>✅ jQuery and SweetAlert should be loaded</li>
        </ul>
    </div>
    
    <script>
    // Additional test when page loads
    window.onload = function() {
        console.log('Page fully loaded - final test:');
        testFunctions();
    };
    </script>
</body>
</html>
