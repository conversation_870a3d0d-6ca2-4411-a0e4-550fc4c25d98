-- Add essential pricing and service fields to tabl_workers_category table
-- Only necessary fields for pricing and basic service information

-- Add new columns to tabl_workers_category table
ALTER TABLE `tabl_workers_category`
ADD COLUMN `price` decimal(10,2) DEFAULT NULL COMMENT 'Service price for this category',
ADD COLUMN `price_type` enum('fixed','hourly','negotiable') DEFAULT 'negotiable' COMMENT 'Type of pricing - fixed, hourly, or negotiable',
ADD COLUMN `availability` text COMMENT 'Working hours and days when worker is available',
ADD COLUMN `min_service_hours` decimal(3,1) DEFAULT NULL COMMENT 'Minimum hours to charge for (e.g., 3.0 for minimum 3 hours)';

-- Add indexes for better performance
ALTER TABLE `tabl_workers_category`
ADD INDEX `idx_price` (`price`),
ADD INDEX `idx_price_type` (`price_type`);

-- Update existing records with default values (optional)
UPDATE `tabl_workers_category` SET
    `price_type` = 'negotiable'
WHERE `price_type` IS NULL;

-- Sample data update (you can customize these based on your needs)
-- UPDATE `tabl_workers_category` SET
--     `price` = 500.00,
--     `price_type` = 'hourly',
--     `availability` = 'Mon-Sat: 9AM-6PM, Sun: Emergency only',
--     `min_service_hours` = 3.0
-- WHERE `id` = 16; -- Example for a specific worker

-- UPDATE `tabl_workers_category` SET
--     `price` = 1200.00,
--     `price_type` = 'fixed',
--     `availability` = 'Mon-Fri: 8AM-8PM, Sat: 9AM-5PM',
--     `min_service_hours` = 2.0
-- WHERE `id` = 17; -- Example for another worker

-- Enhancement completed successfully
-- The tabl_workers_category table now includes essential fields:
-- 1. price - Service price (decimal)
-- 2. price_type - fixed, hourly, or negotiable
-- 3. availability - Working hours and days
-- 4. min_service_hours - Minimum hours to charge (e.g., 3.0 for minimum 3 hours)

-- Examples of usage:
-- Fixed price: price = 1500.00, price_type = 'fixed'
-- Hourly rate: price = 500.00, price_type = 'hourly', min_service_hours = 3.0
-- Negotiable: price = NULL, price_type = 'negotiable'
