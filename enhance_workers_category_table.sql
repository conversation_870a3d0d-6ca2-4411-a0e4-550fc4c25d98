-- Enhance tabl_workers_category table with price and additional necessary fields
-- This will make the worker category profiles more complete and functional

-- Add new columns to tabl_workers_category table
ALTER TABLE `tabl_workers_category` 
ADD COLUMN `price` decimal(10,2) DEFAULT NULL COMMENT 'Service price for this category',
ADD COLUMN `min_price` decimal(10,2) DEFAULT NULL COMMENT 'Minimum service price',
ADD COLUMN `max_price` decimal(10,2) DEFAULT NULL COMMENT 'Maximum service price',
ADD COLUMN `price_type` enum('fixed','hourly','negotiable','range') DEFAULT 'negotiable' COMMENT 'Type of pricing',
ADD COLUMN `service_area` text COMMENT 'Areas where worker provides service',
ADD COLUMN `availability` text COMMENT 'Working hours and days',
ADD COLUMN `specialization` text COMMENT 'Special skills or expertise in this category',
ADD COLUMN `certifications` text COMMENT 'Relevant certifications or qualifications',
ADD COLUMN `tools_equipment` text COMMENT 'Tools and equipment worker has',
ADD COLUMN `years_experience` int DEFAULT NULL COMMENT 'Years of experience in this category',
ADD COLUMN `portfolio_images` text COMMENT 'JSON array of portfolio image filenames',
ADD COLUMN `service_guarantee` varchar(255) DEFAULT NULL COMMENT 'Service guarantee offered',
ADD COLUMN `emergency_service` tinyint(1) DEFAULT 0 COMMENT '1 = offers emergency service, 0 = no',
ADD COLUMN `home_visit` tinyint(1) DEFAULT 1 COMMENT '1 = visits customer location, 0 = customer comes to worker',
ADD COLUMN `advance_payment` decimal(5,2) DEFAULT NULL COMMENT 'Advance payment percentage required',
ADD COLUMN `cancellation_policy` text COMMENT 'Cancellation policy details',
ADD COLUMN `rating_avg` decimal(3,2) DEFAULT 0.00 COMMENT 'Average rating for this category',
ADD COLUMN `total_bookings` int DEFAULT 0 COMMENT 'Total completed bookings in this category',
ADD COLUMN `response_time` varchar(50) DEFAULT NULL COMMENT 'Typical response time',
ADD COLUMN `languages_spoken` varchar(255) DEFAULT NULL COMMENT 'Languages worker can communicate in',
ADD COLUMN `insurance_covered` tinyint(1) DEFAULT 0 COMMENT '1 = has insurance coverage, 0 = no',
ADD COLUMN `background_verified` tinyint(1) DEFAULT 0 COMMENT '1 = background verified, 0 = not verified',
ADD COLUMN `featured` tinyint(1) DEFAULT 0 COMMENT '1 = featured worker, 0 = regular',
ADD COLUMN `meta_title` varchar(255) DEFAULT NULL COMMENT 'SEO meta title',
ADD COLUMN `meta_description` text COMMENT 'SEO meta description',
ADD COLUMN `keywords` text COMMENT 'SEO keywords';

-- Add indexes for better performance
ALTER TABLE `tabl_workers_category`
ADD INDEX `idx_price` (`price`),
ADD INDEX `idx_price_type` (`price_type`),
ADD INDEX `idx_years_experience` (`years_experience`),
ADD INDEX `idx_emergency_service` (`emergency_service`),
ADD INDEX `idx_home_visit` (`home_visit`),
ADD INDEX `idx_rating_avg` (`rating_avg`),
ADD INDEX `idx_total_bookings` (`total_bookings`),
ADD INDEX `idx_featured` (`featured`),
ADD INDEX `idx_background_verified` (`background_verified`);

-- Update existing records with default values (optional)
UPDATE `tabl_workers_category` SET 
    `price_type` = 'negotiable',
    `home_visit` = 1,
    `emergency_service` = 0,
    `rating_avg` = 0.00,
    `total_bookings` = 0,
    `featured` = 0,
    `background_verified` = 0,
    `insurance_covered` = 0
WHERE `price_type` IS NULL;

-- Sample data update (you can customize these based on your needs)
-- UPDATE `tabl_workers_category` SET 
--     `price` = 500.00,
--     `min_price` = 300.00,
--     `max_price` = 1000.00,
--     `price_type` = 'range',
--     `service_area` = 'Within 10km radius',
--     `availability` = 'Mon-Sat: 9AM-6PM, Sun: Emergency only',
--     `specialization` = 'Expert in residential and commercial work',
--     `years_experience` = 5,
--     `service_guarantee` = '30 days service warranty',
--     `emergency_service` = 1,
--     `response_time` = 'Within 2 hours',
--     `languages_spoken` = 'Hindi, English',
--     `background_verified` = 1
-- WHERE `id` = 16; -- Example for a specific worker

-- Create a view for enhanced worker profiles
CREATE OR REPLACE VIEW `view_worker_profiles` AS
SELECT 
    wc.id,
    wc.worker_id,
    wc.cat_id,
    w.name as worker_name,
    w.phone as worker_phone,
    w.email as worker_email,
    w.city as worker_city,
    w.state as worker_state,
    w.dob,
    w.gender,
    c.cat_name,
    c.cat_icon,
    c.description as category_description,
    wc.worker_image,
    wc.work_image,
    wc.experience,
    wc.description as worker_description,
    wc.price,
    wc.min_price,
    wc.max_price,
    wc.price_type,
    wc.service_area,
    wc.availability,
    wc.specialization,
    wc.certifications,
    wc.tools_equipment,
    wc.years_experience,
    wc.portfolio_images,
    wc.service_guarantee,
    wc.emergency_service,
    wc.home_visit,
    wc.advance_payment,
    wc.cancellation_policy,
    wc.rating_avg,
    wc.total_bookings,
    wc.response_time,
    wc.languages_spoken,
    wc.insurance_covered,
    wc.background_verified,
    wc.featured,
    wc.status,
    wc.cat_status,
    wc.created_at,
    wc.updated_at
FROM 
    tabl_workers_category wc
    INNER JOIN tabl_workers w ON wc.worker_id = w.worker_id
    INNER JOIN tabl_categories c ON wc.cat_id = c.cat_id
WHERE 
    wc.status = 1 
    AND wc.cat_status = 1 
    AND w.status = 1 
    AND w.is_deleted = 0;

-- Enhancement completed successfully
-- The tabl_workers_category table now includes:
-- 1. Pricing information (fixed, hourly, negotiable, range)
-- 2. Service details (area, availability, specialization)
-- 3. Professional information (certifications, experience, tools)
-- 4. Business features (guarantee, emergency service, insurance)
-- 5. Performance metrics (ratings, bookings, response time)
-- 6. Verification status (background check, featured status)
-- 7. SEO optimization fields
-- 8. Enhanced indexing for better performance
