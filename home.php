<?php
require_once('admin/lib/db_connection.php');
require_once('lib/auth.php');
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GRS - Home </title>
    <link rel="stylesheet" href="assets/css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous" />

    <!--  -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!--  -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baloo+Bhai+2:wght@400..800&display=swap" rel="stylesheet">
    <!--  -->


</head>
<style>
    body {
        /* background-color: #eee; */
        font-family: "Baloo Bhai 2";
    }

    a {
        text-decoration: none;
    }

    .view-all {
        background-color: #2db79ffc;
        border-radius: 30px;
        color: #ffffff;
        font-size: 16px;
        font-weight: 600;
        padding: 7px 26px;
    }

    .right-content {
        margin-left: 17px;
    }

    .right-content p {
        margin-top: -6px;
        color: grey;
        margin-bottom: 5px;
        font-size: 15px;
    }

    .right-content h4 {
        font-size: 19px;
        color: black;
    }

    .input-group {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: stretch;
        width: 100%;
        box-shadow: 0px 2px 2px 0px #2c4d7c;
        /* font-weight: 600; */
    }

    .input-group-text {
        display: flex;
        align-items: center;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #ffffff;
        text-align: center;
        white-space: nowrap;
        background-color: #22b2a5;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }

    .form-select {
        font-weight: 500;
        color: #282b2f;
    }

    .bannnerimg {
        border-radius: 10px;
    }

    p {
        font-weight: 500;
    }

    /* .categoryicon {
        height: 100px;
    } */

    .img-fluid {
        height: 60px;
    }

    .card {
        border: 1px solid rgb(0, 153, 255);
        border-radius: 15px;
        box-shadow: 1px 1px 1px 1px #3368a4;
    }

    .cattext {
        font-weight: 600;
    }

    .headingname {
        font-weight: 700;
        color: #303069;
        font-size: 25px;
    }

    /* Modern Section Headers */
    .section-header {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: #667eea;
        font-size: 1.8rem;
    }

    .section-subtitle {
        color: #7f8c8d;
        font-size: 1rem;
        margin: 0;
        font-weight: 500;
    }

    .btn-view-all {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-view-all:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    /* Modern Category Section */
    .category-section-wrapper {
        padding: 3rem 0;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        margin: 2rem 0;
    }

    .categories-container {
        padding: 0;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 2rem;
        padding: 1rem 0;
        justify-items: center;
    }

    .category-card-modern {
        background: white;
        border-radius: 20px;
        padding: 2rem 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
        width: 100%;
        max-width: 250px;
        margin: 0 auto;
    }

    .category-card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .category-card-modern:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0,0,0,0.2);
    }

    .category-link {
        text-decoration: none;
        color: inherit;
        display: block;
    }

    .category-icon-wrapper {
        position: relative;
        margin-bottom: 1.5rem;
        display: flex;
        justify-content: center;
    }

    .category-icon-container {
        width: 90px;
        height: 90px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
    }

    .category-icon {
        width: 50px;
        height: 50px;
        object-fit: contain;
        filter: brightness(0) invert(1);
        max-width: 50px;
        max-height: 50px;
    }

    /* Fallback for missing category icons */
    .category-icon-container::before {
        content: '🔧';
        font-size: 2rem;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
    }

    .category-icon {
        position: relative;
        z-index: 2;
    }

    .category-icon:not([src]), .category-icon[src=""] {
        display: none;
    }

    .worker-count-badge {
        position: absolute;
        top: -5px;
        right: 20px;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 4px 8px;
        font-size: 0.75rem;
        font-weight: 600;
        min-width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .category-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .category-workers {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    .carousel-container {
        position: relative;
    }

    .carousel-indicators {
        position: static;
        padding-top: 5px;
        /* Adjust as needed */
    }

    .carousel-indicators li {
        background-color: #204b69;
        /* Or any color you prefer */
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin: 0 5px;
        /* Spacing between dots */
    }

    .carousel-indicators .active {
        background-color: #d4d0e6;
        /* Active indicator color */
    }

    .carousel-item img {
        border-radius: 39px 1px 45px 0px;
    }

    .btn-view {
        /* border: 1px solid #204b69; */
        color: #ffffff;
        font-size: 13px;
        font-weight: 700;
        border-radius: 71px;
        height: 32px;
        background-color: #6969b2;
        background-color: #182b3a;
        background-image: linear-gradient(315deg, #182b3a 0%, #1230b4 74%);
        border: none;

    }

    .imgdiv {
        box-shadow: 0px 0px 7px 1px #1230b4;
        height: 84px;
        width: 84px;
        border-radius: 50%;
        background-color: #fff;
    }

    .card {
        border: 1px solid rgb(14 26 83 / 44%);
        border-radius: 15px;
    }

    a {
        color: black;
    }

    .tcolorone {
        color: black;
    }

    .total-can {
        position: absolute;
        bottom: 1px;
        right: 34px;
        background: rgba(0, 0, 0, 0.5);
        padding: 2px 4px;
        border-radius: 63px;
        color: #fff;
        font-size: 12px;
    }

    /* =============== */
    .headline {
        font-size: 30px;
    }

    .btn-new {
        border: 1px solid;
        background-image: linear-gradient(315deg, #182b3a 0%, #1230b4 74%);
        color: #fff;
        width: 100%;
    }

    /* Modern Profiles Section */
    .profiles-section-wrapper {
        padding: 3rem 0;
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        margin: 2rem 0;
    }

    .profiles-container {
        padding: 0;
    }

    .profiles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        padding: 1rem 0;
        justify-items: center;
    }

    .worker-card-modern {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        position: relative;
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
    }

    .worker-card-modern:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0,0,0,0.2);
    }

    .worker-link {
        text-decoration: none;
        color: inherit;
    }

    .worker-image-container {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .worker-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .worker-card-modern:hover .worker-image {
        transform: scale(1.05);
    }

    .worker-overlay {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .worker-rating {
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .worker-rating i {
        color: #ffd700;
    }

    .worker-details {
        padding: 1.5rem;
    }

    .worker-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .worker-category {
        color: #667eea;
        font-size: 0.95rem;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .worker-info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .worker-location, .worker-experience {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.85rem;
        color: #7f8c8d;
    }

    .worker-price {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        padding: 12px;
        border-radius: 12px;
        text-align: center;
        margin-bottom: 1rem;
    }

    .price-label {
        display: block;
        font-size: 0.8rem;
        color: #7f8c8d;
        margin-bottom: 4px;
    }

    .price-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #667eea;
    }

    .worker-stats {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 12px;
        margin-bottom: 1rem;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-value {
        display: block;
        font-size: 1.1rem;
        font-weight: 700;
        color: #2c3e50;
    }

    .stat-label {
        font-size: 0.75rem;
        color: #7f8c8d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-divider {
        width: 1px;
        height: 30px;
        background: #dee2e6;
    }

    .worker-actions {
        padding: 0 1.5rem 1.5rem;
    }

    .btn-book-service {
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-book-service:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .no-data-message {
        text-align: center;
        padding: 3rem;
        color: #7f8c8d;
        grid-column: 1 / -1;
    }

    .no-data-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #bdc3c7;
    }

    .no-data-message h4 {
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .worker-info {
        padding: 10px 10px;
        border: none;
        box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
        border-radius: 10px;
    }

    .scroll {
        /* overflow: scroll; */
        /* border: 1px solid; */
        gap: 10px;
        padding: 10px 5px;
    }

    .scroll img {
        /* margin-left: 10px; */
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        width: 100%;
    }

    .btn-x {
        border: none;
        background-color: transparent;
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
        position: relative;
        width: 100%;
        margin: 10px;
        background-color: #eee;
        border-bottom: 1px solid #1230b4;
    }

    .active-btn {
        background-color: #1230b4;
        color: #fff;
    }

    #business,
    #work {
        margin-top: 20px;
    }

    /* Optional styling for hover */
    .btn-x:hover {
        border-bottom: 2px solid lightgray;
    }

    .ul {
        padding: 0;
    }

    .ul li {
        margin-bottom: 10px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .section-title {
            font-size: 1.5rem;
        }

        .section-title i {
            font-size: 1.3rem;
        }

        .btn-view-all {
            padding: 10px 16px;
            font-size: 0.9rem;
        }

        .category-section-wrapper, .profiles-section-wrapper {
            padding: 2rem 0;
            margin: 1rem 0;
        }

        .categories-grid {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1.5rem;
        }

        .category-card-modern {
            padding: 1.5rem 1rem;
            max-width: 200px;
        }

        .category-icon-container {
            width: 70px;
            height: 70px;
        }

        .category-icon {
            width: 40px;
            height: 40px;
        }

        .profiles-grid {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }

        .worker-card-modern {
            max-width: 320px;
        }

        .worker-details {
            padding: 1rem;
        }

        .worker-name {
            font-size: 1.1rem;
        }

        .worker-actions {
            padding: 0 1rem 1rem;
        }
    }

    @media (max-width: 480px) {
        .section-header {
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.3rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .btn-view-all {
            padding: 8px 12px;
            font-size: 0.85rem;
        }

        .category-section-wrapper, .profiles-section-wrapper {
            padding: 1.5rem 0;
            margin: 0.5rem 0;
        }

        .categories-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .category-card-modern {
            padding: 1rem 0.5rem;
            max-width: 160px;
        }

        .category-icon-container {
            width: 60px;
            height: 60px;
        }

        .category-icon {
            width: 35px;
            height: 35px;
        }

        .category-name {
            font-size: 0.95rem;
        }

        .category-workers {
            font-size: 0.8rem;
        }

        .profiles-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .worker-card-modern {
            max-width: 100%;
        }

        .worker-info-row {
            flex-direction: column;
            gap: 0.5rem;
        }

        .worker-stats {
            gap: 0.5rem;
        }

        .worker-details {
            padding: 0.75rem;
        }

        .worker-actions {
            padding: 0 0.75rem 0.75rem;
        }
    }
</style>

<body>
    <!-- header -->
    <?php require_once('inc/header.php'); ?>
    <!-- /header -->
    <!-- carousel start-->
    <div class="main">
        <div class="container mt-3">
            <div class="carousel-container">
                <!-- Carousel -->
                <div id="carouselExampleIndicators" class="carousel slide" data-ride="carousel">
                    <div class="carousel-inner">
                        <div class="carousel-item active">
                            <img src="assets/images/1.webp" class="d-block w-100" alt="First slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/2.webp" class="d-block w-100" alt="Second slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/3.webp" class="d-block w-100" alt="Third slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/4.webp" class="d-block w-100" alt="Fourth slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/5.webp" class="d-block w-100" alt="Five slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/6.webp" class="d-block w-100" alt="Six slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/7.webp" class="d-block w-100" alt="Seven slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/8.webp" class="d-block w-100" alt="Eight slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/9.webp" class="d-block w-100" alt="Nine slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/10.webp" class="d-block w-100" alt="Ten slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/11.webp" class="d-block w-100" alt="Eleven slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/12.jpg" class="d-block w-100" alt="Twelve slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/13.jpg" class="d-block w-100" alt="Thirteen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/14.jpg" class="d-block w-100" alt="Fourteen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/15.jpg" class="d-block w-100" alt="Fifteen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/16.jpeg" class="d-block w-100" alt="Sixteen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/17.jpeg" class="d-block w-100" alt="Seventeen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/18.jpg" class="d-block w-100" alt="Eighteen slide">
                        </div>

                    </div>
                    <a class="carousel-control-prev" href="#carouselExampleIndicators" role="button" data-slide="prev">
                        <span class="carousel-control-prev-icon d-none" aria-hidden="true"></span>
                        <span class="sr-only">Previous</span>
                    </a>
                    <a class="carousel-control-next" href="#carouselExampleIndicators" role="button" data-slide="next">
                        <span class="carousel-control-next-icon d-none" aria-hidden="true"></span>
                        <span class="sr-only">Next</span>
                    </a>
                    <!-- Indicators -->
                </div>
            </div>

        </div>

        <!-- category start -->
        <div class="category-section-wrapper">
            <div class="container">
                <div class="section-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="section-title">
                                <i class="fa-solid fa-th-large"></i> Service Categories
                            </h2>
                            <p class="section-subtitle">Find the perfect service for your needs</p>
                        </div>
                        <a href="category.php" class="btn btn-view-all">
                            <i class="fa-solid fa-arrow-right"></i> View All
                        </a>
                    </div>
                </div>

                <div class="categories-container">
                    <div class="categories-grid">
                        <?php
                        $catQuery = dbQuery("SELECT * FROM tabl_categories WHERE cat_status=1 ORDER BY cat_id DESC LIMIT 8");
                        if (dbNumRows($catQuery) > 0) {
                            while ($catData = dbFetchAssoc($catQuery)):
                                // getting total candidates in a category
                                $candidateQuery = dbQuery("SELECT count(id) AS total FROM tabl_workers_category WHERE cat_id='" . $catData['cat_id'] . "' AND status='1'");
                                $candidates = dbFetchAssoc($candidateQuery);

                                // Fix image path - try multiple possible paths
                                $imagePaths = [
                                    "admin/assets/img/category/thumb-50/" . $catData['cat_icon'],
                                    "admin/assets/img/category/" . $catData['cat_icon'],
                                    "assets/images/categories/" . $catData['cat_icon'],
                                    "assets/images/category-icons/" . $catData['cat_icon']
                                ];

                                $validImagePath = "assets/images/default-category.png"; // default fallback
                                foreach ($imagePaths as $path) {
                                    if (file_exists($path)) {
                                        $validImagePath = $path;
                                        break;
                                    }
                                }
                        ?>
                                <div class="category-card-modern">
                                    <a href="category.php?id=<?= $catData['cat_id'] ?>" class="category-link">
                                        <div class="category-icon-wrapper">
                                            <div class="category-icon-container">
                                                <img class="category-icon"
                                                     src="<?= $validImagePath ?>"
                                                     alt="<?= $catData['cat_name'] ?>"
                                                     onerror="this.src='assets/images/default-category.png'">
                                            </div>
                                            <?php if ($candidates['total'] > 0): ?>
                                            <div class="worker-count-badge">
                                                <span><?= $candidates['total'] ?></span>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="category-info">
                                            <h5 class="category-name"><?= $catData['cat_name'] ?></h5>
                                            <p class="category-workers"><?= $candidates['total'] ?> Workers Available</p>
                                        </div>
                                    </a>
                                </div>
                        <?php
                            endwhile;
                        } else {
                            echo '<div class="no-data-message">
                                    <i class="fa-solid fa-exclamation-circle"></i>
                                    <h4>No Categories Found</h4>
                                    <p>Categories will be displayed here once they are added.</p>
                                  </div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        <!-- category end -->

        <!-- profiles section start -->
        <div class="profiles-section-wrapper">
            <div class="container">
                <div class="section-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="section-title">
                                <i class="fa-solid fa-users"></i> Featured Workers
                            </h2>
                            <p class="section-subtitle">Connect with skilled professionals in your area</p>
                        </div>
                        <a href="view_profiles.php" class="btn btn-view-all">
                            <i class="fa-solid fa-arrow-right"></i> View All
                        </a>
                    </div>
                </div>

                <div class="profiles-container">
                    <div class="profiles-grid">
                    <?php
                    $profileQuery = dbQuery("SELECT wc.id, wc.worker_image, wc.work_image, wc.experience, wc.price, wc.price_type,
                                           w.name, w.phone, w.city, wc.worker_id, wc.cat_id
                        FROM tabl_workers_category wc
                        INNER JOIN tabl_workers w ON wc.worker_id = w.worker_id
                        WHERE w.status = 1 AND wc.cat_status = 1 AND wc.status = 1
                        ORDER BY wc.id DESC LIMIT 12");

                    if (dbNumRows($profileQuery) > 0) {
                        while ($proData = dbFetchAssoc($profileQuery)):
                            // cat detail
                            $catDetails = dbFetchAssoc(dbQuery("SELECT cat_name FROM tabl_categories WHERE cat_id=" . $proData['cat_id']));

                            // Get rating data
                            $ratingQuery = dbQuery("SELECT AVG(rating) as avg_rating, COUNT(*) as total_ratings
                                                  FROM tabl_worker_ratings WHERE worker_id = " . $proData['id']);
                            $ratingData = dbFetchAssoc($ratingQuery);
                            $avgRating = round($ratingData['avg_rating'] ?? 0, 1);
                            $totalRatings = $ratingData['total_ratings'] ?? 0;

                            // Format price display
                            $priceDisplay = 'Price on Request';
                            if (!empty($proData['price']) && $proData['price'] > 0) {
                                if ($proData['price_type'] === 'hourly') {
                                    $priceDisplay = '₹' . number_format($proData['price'], 0) . '/hr';
                                } elseif ($proData['price_type'] === 'fixed') {
                                    $priceDisplay = '₹' . number_format($proData['price'], 0);
                                } else {
                                    $priceDisplay = '₹' . number_format($proData['price'], 0);
                                }
                            }
                    ?>
                            <div class="worker-card-modern">
                                <a href="me.php?id=<?= $proData['id']; ?>" class="worker-link">
                                    <div class="worker-image-container">
                                        <img src="worker/assets/images/workers/workers-img/<?= $proData['worker_image']; ?>"
                                             class="worker-image"
                                             onerror="this.src='assets/images/profile.jpg'"
                                             alt="<?= $proData['name'] ?>">
                                        <div class="worker-overlay">
                                            <div class="worker-rating">
                                                <i class="fa-solid fa-star"></i>
                                                <span><?= $avgRating ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="worker-details">
                                        <h5 class="worker-name"><?= $proData['name'] ?></h5>
                                        <p class="worker-category">
                                            <i class="fa-solid fa-briefcase"></i>
                                            <?= $catDetails['cat_name'] ?>
                                        </p>

                                        <div class="worker-info-row">
                                            <div class="worker-location">
                                                <i class="fa-solid fa-map-marker-alt"></i>
                                                <span><?= $proData['city'] ?? 'Location' ?></span>
                                            </div>
                                            <div class="worker-experience">
                                                <i class="fa-solid fa-medal"></i>
                                                <span><?= $proData['experience'] ?? '0' ?>Y</span>
                                            </div>
                                        </div>

                                        <div class="worker-price">
                                            <span class="price-label">Starting at</span>
                                            <span class="price-value"><?= $priceDisplay ?></span>
                                        </div>

                                        <div class="worker-stats">
                                            <div class="stat-item">
                                                <span class="stat-value"><?= $totalRatings ?></span>
                                                <span class="stat-label">Reviews</span>
                                            </div>
                                            <div class="stat-divider"></div>
                                            <div class="stat-item">
                                                <span class="stat-value"><?= $avgRating ?></span>
                                                <span class="stat-label">Rating</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>

                                <div class="worker-actions">
                                    <a href="me.php?id=<?= $proData['id']; ?>" class="btn btn-book-service">
                                        <i class="fa-solid fa-calendar-check"></i> Book Service
                                    </a>
                                </div>
                            </div>
                    <?php
                        endwhile;
                    } else {
                        echo '<div class="no-data-message">
                                <i class="fa-solid fa-users-slash"></i>
                                <h4>No Workers Found</h4>
                                <p>Check back later for available workers in your area.</p>
                              </div>';
                    }
                    ?>
                    </div>
                </div>
            </div>
        </div>
        <!-- profiles section end -->
                <style>
                    /* .card-row {
                        display: flex;
                        overflow-x: auto;
                        gap: 16px;                        
                        padding: 12px;
                    } */

                    /* .card {
                        flex: 0 0 33.33%;                       
                    } */
                </style>
                <!-- <div class="container mt-4 carddiv">
                    <div class="card-row">

                        <div class="card shadow cardcol pb-3">
                            <img src="assets/images/profile3.png" class="card-img-top" alt="...">
                            <div class="card-body p-0 pt-1">
                                <hr>
                                <div class="d-flex" style="gap: 25px;">
                                    <div class="wicon">
                                        <i class="bi bi-whatsapp"></i>
                                    </div>

                                    <span class="calltext">Call</span>
                                </div>

                            </div>
                        </div>
                        <div class="card shadow cardcol pb-3">
                            <img src="assets/images/profile4.png" class="card-img-top" alt="...">
                            <div class="card-body p-0 pt-1">
                                <hr>
                                <div class="d-flex" style="gap: 25px;">
                                    <div class="wicon">
                                        <i class="bi bi-whatsapp"></i>
                                    </div>

                                    <span class="calltext">Call</span>
                                </div>
                            </div>
                        </div>
                        <div class="card shadow cardcol pb-3">
                            <img src="assets/images/profile5.png" class="card-img-top" alt="...">
                            <div class="card-body p-0 pt-1">
                                <hr>
                                <div class="d-flex" style="gap: 25px;">
                                    <div class="wicon">
                                        <i class="bi bi-whatsapp"></i>
                                    </div>

                                    <span class="calltext">Call</span>
                                </div>
                            </div>
                        </div>
                        <div class="card shadow cardcol pb-3">
                            <img src="assets/images/profile6.png" class="card-img-top" alt="...">
                            <div class="card-body p-0 pt-1">
                                <hr>
                                <div class="d-flex" style="gap: 25px;">
                                    <div class="wicon">
                                        <i class="bi bi-whatsapp"></i>
                                    </div>

                                    <span class="calltext">Call</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->

                <!-- Add this CSS for styling -->


            </div>

        </div>
        <!-- card-section end -->

        <div class="container mt-3">
            <div class="worker-info">
                <h5 class="headline">Connect with thousands of workers near you</h5>
                <p>GRSP intelligently matches businesses with our vetted pool of 6 million workers whether you need them for a few hours, a few months, or anything in between.</p>
                <!-- <a href="register.php" class="btn btn-new">FIND WORK</a> -->

                <a href="./worker/workerregister.php" class="btn btn-new">FIND WORK</a>
            </div>

            <div class="galley mt-3">
                <div class="d-flex scroll">
                    <div>
                        <img src="./assets/images/galley/1.png" alt="">
                    </div>
                    <div>
                        <img src="./assets/images/galley/2.png" alt="">
                    </div>
                    <div>
                        <img src="./assets/images/galley/1.png" alt="">
                    </div>
                    <!-- <div>
                            <img src="./assets/images/galley/2.png" alt="">
                        </div> -->
                </div>
            </div>
            <div class="worker-info">
                <h5 class="headline">Struggling to find qualified and reliable workers? </h5>
                <p>It's getting harder to find hourly workers that are a fit for your business through traditional staffing agencies. Instawork's platform makes it easy to find the qualified and reliable workers you need.</p>
            </div>
            <!--  -->
            <div class="worker-info">
                <p>ECONOMIC RESEARCH </p>
                <h5 class="headline">Real time metrics on the labor market</h5>
                <p>Local labor market insights and reports that explore the trends and statistics that affect your business</p>
                <a href="about.php" class="btn btn-new">EXPLORE MORE</a>
            </div>
            <!--  -->
            <div class="bus_work">
                <div class="d-flex">
                    <button class="btn-x active-btn" id="btn-business" onclick="showBusiness()">Business</button>
                    <button class="btn-x" id="btn-work" onclick="showWork()">Work</button>
                </div>
                <div class="business" id="business">
                    <h5 class="headline">Fill your shifts in a few clicks.</h5>
                    <ul class="ul">
                        <li>1. <strong>Post a shift</strong> Enter your shift details like role, start/end time, and pay rates. Add instructions like attire or physical requirements (e g. standing, lifting).</li>
                        <li>2. Match with workers in minutes</li>
                        <li>3. Workers arrive, we do the rest</li>
                    </ul>
                </div>
                <div class="work" id="work" style="display:none;">
                    <h5 class="headline">Be your own boss. Get paid instantly.</h5>
                    <ul class="ul">
                        <li>1. Create your profile</li>
                        <li>2. Pick up a shift</li>
                        <li>3. Work the shift and get paid</li>
                        <li>4. Earn bonuses and rewards</li>
                    </ul>
                    <p>Our Top Pro Program offers cash bonuses for applicable shifts.</p>
                </div>
            </div>


        </div>
    </div>

    <!-- carousel end -->


    <div class="p-5"></div>



    <!-- footer -->
    <?php require_once('inc/footer.php'); ?>
    <!-- /footer  -->

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.0.5/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
        integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous">
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"
        integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy" crossorigin="anonymous">
    </script>
    <script>
        function showBusiness() {
            document.getElementById("business").style.display = "block";
            document.getElementById("work").style.display = "none";

            // Toggle active button
            document.getElementById("btn-business").classList.add("active-btn");
            document.getElementById("btn-work").classList.remove("active-btn");
        }

        function showWork() {
            document.getElementById("business").style.display = "none";
            document.getElementById("work").style.display = "block";

            // Toggle active button
            document.getElementById("btn-business").classList.remove("active-btn");
            document.getElementById("btn-work").classList.add("active-btn");
        }
    </script>
    <script>
        function removeScrollBarPushing() {
            const offsetY = document.documentElement.scrollTop;
            let i = 0;
            const time = setInterval(function() {
                if (i++ < 2) {
                    clearInterval(time);
                }
                document.documentElement.scrollTop = offsetY;
            }, 1);
        }

        // open sidenav
        document
            .getElementById("nav-toggle-btn")
            .addEventListener("click", function() {
                document.getElementById("sidenav").classList.add("show");
                removeScrollBarPushing();
            });
        // close sidenav
        document
            .querySelector("#sidenav .closebtn")
            .addEventListener("click", function() {
                document.getElementById("sidenav").classList.remove("show");
            });

        $(".hamburger").on("click", function() {
            $(this).parent().toggleClass("active");
        });

        function toggleSearchInput() {
            var searchInput = document.querySelector(".search-input");
            searchInput.style.display =
                searchInput.style.display === "none" ||
                searchInput.style.display === "" ?
                "block" :
                "none";
        }
    </script>
</body>

</html>