<?php
require_once('admin/lib/db_connection.php');
require_once('lib/auth.php');
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GRS - Home </title>
    <link rel="stylesheet" href="assets/css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous" />

    <!--  -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!--  -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baloo+Bhai+2:wght@400..800&display=swap" rel="stylesheet">
    <!--  -->


</head>
<style>
    body {
        /* background-color: #eee; */
        font-family: "Baloo Bhai 2";
    }

    a {
        text-decoration: none;
    }

    .view-all {
        background-color: #2db79ffc;
        border-radius: 30px;
        color: #ffffff;
        font-size: 16px;
        font-weight: 600;
        padding: 7px 26px;
    }

    .right-content {
        margin-left: 17px;
    }

    .right-content p {
        margin-top: -6px;
        color: grey;
        margin-bottom: 5px;
        font-size: 15px;
    }

    .right-content h4 {
        font-size: 19px;
        color: black;
    }

    .input-group {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: stretch;
        width: 100%;
        box-shadow: 0px 2px 2px 0px #2c4d7c;
        /* font-weight: 600; */
    }

    .input-group-text {
        display: flex;
        align-items: center;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #ffffff;
        text-align: center;
        white-space: nowrap;
        background-color: #22b2a5;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }

    .form-select {
        font-weight: 500;
        color: #282b2f;
    }

    .bannnerimg {
        border-radius: 10px;
    }

    p {
        font-weight: 500;
    }

    /* .categoryicon {
        height: 100px;
    } */

    .img-fluid {
        height: 60px;
    }

    .card {
        border: 1px solid rgb(0, 153, 255);
        border-radius: 15px;
        box-shadow: 1px 1px 1px 1px #3368a4;
    }

    .cattext {
        font-weight: 600;
    }

    .headingname {
        font-weight: 700;
        color: #303069;
        font-size: 25px;
    }

    /* Container for the category section */
    .category-section {
        overflow: auto;
    }

    /* Scrolling container */
    .category-scroll-container {
        display: flex;
        overflow-x: scroll;
        direction: ltr;
        padding: 10px 0px;
        /* This causes the scroll direction to be from right to left */
    }


    /* Inner container for categories */
    .category-row {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: flex-end;
        direction: ltr;
        /* Ensures the text within the categories is still left-to-right */
    }

    /* Styling for each category card */
    .category-card {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 10px;
        margin-right: 10px;
        border: 1px solid #ccc;
        border-radius: 8px;
    }

    /* Styling for the category icon */
    .category-icon {
        width: 50px;
        height: 50px;
    }

    .catgorydata {
        margin-left: 20px;
    }

    .imgdiv {
        box-shadow: 0px 0px 3px 1px #1d84f6;
        height: 89px;
        width: 88px;
        border-radius: 50%;
    }

    .carousel-container {
        position: relative;
    }

    .carousel-indicators {
        position: static;
        padding-top: 5px;
        /* Adjust as needed */
    }

    .carousel-indicators li {
        background-color: #204b69;
        /* Or any color you prefer */
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin: 0 5px;
        /* Spacing between dots */
    }

    .carousel-indicators .active {
        background-color: #d4d0e6;
        /* Active indicator color */
    }

    .carousel-item img {
        border-radius: 39px 1px 45px 0px;
    }

    .btn-view {
        /* border: 1px solid #204b69; */
        color: #ffffff;
        font-size: 13px;
        font-weight: 700;
        border-radius: 71px;
        height: 32px;
        background-color: #6969b2;
        background-color: #182b3a;
        background-image: linear-gradient(315deg, #182b3a 0%, #1230b4 74%);
        border: none;

    }

    .imgdiv {
        box-shadow: 0px 0px 7px 1px #1230b4;
        height: 84px;
        width: 84px;
        border-radius: 50%;
        background-color: #fff;
    }

    .card {
        border: 1px solid rgb(14 26 83 / 44%);
        border-radius: 15px;
    }

    a {
        color: black;
    }

    .tcolorone {
        color: black;
    }

    .total-can {
        position: absolute;
        bottom: 1px;
        right: 34px;
        background: rgba(0, 0, 0, 0.5);
        padding: 2px 4px;
        border-radius: 63px;
        color: #fff;
        font-size: 12px;
    }

    /* =============== */
    .headline {
        font-size: 30px;
    }

    .btn-new {
        border: 1px solid;
        background-image: linear-gradient(315deg, #182b3a 0%, #1230b4 74%);
        color: #fff;
        width: 100%;
    }

    .worker-info {
        padding: 10px 10px;
        border: none;
        box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
        border-radius: 10px;
    }

    .scroll {
        /* overflow: scroll; */
        /* border: 1px solid; */
        gap: 10px;
        padding: 10px 5px;
    }

    .scroll img {
        /* margin-left: 10px; */
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        width: 100%;
    }

    .btn-x {
        border: none;
        background-color: transparent;
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
        position: relative;
        width: 100%;
        margin: 10px;
        background-color: #eee;
        border-bottom: 1px solid #1230b4;
    }

    .active-btn {
        background-color: #1230b4;
        color: #fff;
    }

    #business,
    #work {
        margin-top: 20px;
    }

    /* Optional styling for hover */
    .btn-x:hover {
        border-bottom: 2px solid lightgray;
    }

    .ul {
        padding: 0;
    }

    .ul li {
        margin-bottom: 10px;
    }
</style>

<body>
    <!-- header -->
    <?php require_once('inc/header.php'); ?>
    <!-- /header -->
    <!-- carousel start-->
    <div class="main">
        <div class="container mt-3">
            <div class="carousel-container">
                <!-- Carousel -->
                <div id="carouselExampleIndicators" class="carousel slide" data-ride="carousel">
                    <div class="carousel-inner">
                        <div class="carousel-item active">
                            <img src="assets/images/1.webp" class="d-block w-100" alt="First slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/2.webp" class="d-block w-100" alt="Second slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/3.webp" class="d-block w-100" alt="Third slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/4.webp" class="d-block w-100" alt="Fourth slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/5.webp" class="d-block w-100" alt="Five slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/6.webp" class="d-block w-100" alt="Six slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/7.webp" class="d-block w-100" alt="Seven slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/8.webp" class="d-block w-100" alt="Eight slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/9.webp" class="d-block w-100" alt="Nine slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/10.webp" class="d-block w-100" alt="Ten slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/11.webp" class="d-block w-100" alt="Eleven slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/12.jpg" class="d-block w-100" alt="Twelve slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/13.jpg" class="d-block w-100" alt="Thirteen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/14.jpg" class="d-block w-100" alt="Fourteen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/15.jpg" class="d-block w-100" alt="Fifteen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/16.jpeg" class="d-block w-100" alt="Sixteen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/17.jpeg" class="d-block w-100" alt="Seventeen slide">
                        </div>
                        <div class="carousel-item">
                            <img src="assets/images/18.jpg" class="d-block w-100" alt="Eighteen slide">
                        </div>

                    </div>
                    <a class="carousel-control-prev" href="#carouselExampleIndicators" role="button" data-slide="prev">
                        <span class="carousel-control-prev-icon d-none" aria-hidden="true"></span>
                        <span class="sr-only">Previous</span>
                    </a>
                    <a class="carousel-control-next" href="#carouselExampleIndicators" role="button" data-slide="next">
                        <span class="carousel-control-next-icon d-none" aria-hidden="true"></span>
                        <span class="sr-only">Next</span>
                    </a>
                    <!-- Indicators -->
                </div>
            </div>

        </div>

        <!-- category start -->
        <div class="container category-section">

            <div class="conta text-center">
                <div class="d-flex justify-content-between">
                    <h4 class="mb-3 headingname tcolorone">Category</h4>
                    <a href="category.php" class="btn btn-view">View All</a>
                </div>

                <div class="category-scroll-container">
                    <div class="category-row">

                        <?php
                        $catQuery = dbQuery("SELECT * FROM tabl_categories WHERE cat_status=1 ORDER BY cat_id DESC LIMIT 8");
                        if (dbNumRows($catQuery) > 0) {
                            while ($catData = dbFetchAssoc($catQuery)):
                                // geting total candidates in a categories 
                                $candidateQuery = dbQuery("SELECT count(id) AS total FROM tabl_workers_category WHERE cat_id='" . $catData['cat_id'] . "' AND status='1'");
                                $candidates = dbFetchAssoc($candidateQuery);
                        ?>
                                <div class="catgorydata col d-flex justify-content-center align-items-center">
                                    <a href="category.php?id=<?= $catData['cat_id'] ?>">
                                        <div>
                                            <div class="imgdiv d-flex justify-content-center align-items-center" style="position:relative;"><span
                                                    class="categoryicon"><img class="img-fluid" style="border-radius:50%;"
                                                        src="admin/assets/img/category/thumb-50/<?= $catData['cat_icon'] ?>"
                                                        alt=""> <span class="total-can"><?= $candidates['total'] ?></span></span></div>
                                            <div>
                                                <p class="cattext tcolorone"><?= $catData['cat_name'] ?></p>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                        <?php
                            endwhile;
                        }
                        ?>
                    </div>
                </div>


            </div>

        </div>
        <!-- category end -->

        <!-- card-section start -->

        <div class="container sectiocard">

            <div class="conta text-center">
                <div class="d-flex justify-content-between">
                    <h4 class="mb-3 headingname tcolorone">Profile</h4>
                    <a href="view_profiles.php" class="btn btn-view">View All</a>
                </div>
                <div class="row row-cols-1 row-cols-sm-2 row-cols-md-2 row-cols-lg-3">
                    <?php
                    $profileQuery = dbQuery("SELECT wc.id,wc.worker_image, wc.work_image, w.name, w.phone, wc.worker_id , wc.cat_id
                        FROM tabl_workers_category wc
                        INNER JOIN tabl_workers w ON wc.worker_id = w.worker_id
                        WHERE w.status = 1 AND wc.cat_status = 1 AND wc.status = 1 ORDER BY wc.id DESC LIMIT 12");

                    if (dbNumRows($profileQuery) > 0) {
                        while ($proData = dbFetchAssoc($profileQuery)):
                            // cat detail 

                            $catDetails = dbFetchAssoc(dbQuery("SELECT cat_name FROM tabl_categories WHERE cat_id=" . $proData['cat_id']));
                    ?>
                            <div class="col cardcol pb-3" style="max-width:25%;">
                                <div class="card shadow">
                                    <a href="me.php?id=<?= $proData['id']; ?>" style="padding:8px 8px 2px 8px">
                                        <img src="worker/assets/images/workers/workers-img/<?= $proData['worker_image']; ?>"
                                            class="card-img-top" onerror="this.src='assets/images/profile.jpg'" alt="profile">
                                        <p class="p-1 m-0" style="text-align:left; font-size:12px;font-weight:700;overflow:hidden;width:100%;text-wrap:nowrap"><?= $proData['name'] ?></p>
                                        <p class="p-1 m-0" style="text-align:left; font-size:12px; font-weight:600;overflow:hidden;width:100%;text-wrap:nowrap"><?= $catDetails['cat_name'] ?></p>
                                    </a>
                                    <div class="card-body p-2 m-0" style="border-top:2px solid #1230b4">
                                        <div class="text-center">
                                            <a href="me.php?id=<?= $proData['id']; ?>" class="btn btn-primary btn-sm w-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 20px; font-weight: 600;">
                                                <i class="fa-solid fa-calendar-check"></i> Book Service
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    <?php
                        endwhile;
                    } else {
                        echo '<p class="mt-4">No Data Found! 😥</p>';
                    }
                    ?>
                </div>
                <style>
                    /* .card-row {
                        display: flex;
                        overflow-x: auto;
                        gap: 16px;                        
                        padding: 12px;
                    } */

                    /* .card {
                        flex: 0 0 33.33%;                       
                    } */
                </style>
                <!-- <div class="container mt-4 carddiv">
                    <div class="card-row">

                        <div class="card shadow cardcol pb-3">
                            <img src="assets/images/profile3.png" class="card-img-top" alt="...">
                            <div class="card-body p-0 pt-1">
                                <hr>
                                <div class="d-flex" style="gap: 25px;">
                                    <div class="wicon">
                                        <i class="bi bi-whatsapp"></i>
                                    </div>

                                    <span class="calltext">Call</span>
                                </div>

                            </div>
                        </div>
                        <div class="card shadow cardcol pb-3">
                            <img src="assets/images/profile4.png" class="card-img-top" alt="...">
                            <div class="card-body p-0 pt-1">
                                <hr>
                                <div class="d-flex" style="gap: 25px;">
                                    <div class="wicon">
                                        <i class="bi bi-whatsapp"></i>
                                    </div>

                                    <span class="calltext">Call</span>
                                </div>
                            </div>
                        </div>
                        <div class="card shadow cardcol pb-3">
                            <img src="assets/images/profile5.png" class="card-img-top" alt="...">
                            <div class="card-body p-0 pt-1">
                                <hr>
                                <div class="d-flex" style="gap: 25px;">
                                    <div class="wicon">
                                        <i class="bi bi-whatsapp"></i>
                                    </div>

                                    <span class="calltext">Call</span>
                                </div>
                            </div>
                        </div>
                        <div class="card shadow cardcol pb-3">
                            <img src="assets/images/profile6.png" class="card-img-top" alt="...">
                            <div class="card-body p-0 pt-1">
                                <hr>
                                <div class="d-flex" style="gap: 25px;">
                                    <div class="wicon">
                                        <i class="bi bi-whatsapp"></i>
                                    </div>

                                    <span class="calltext">Call</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->

                <!-- Add this CSS for styling -->


            </div>

        </div>
        <!-- card-section end -->

        <div class="container mt-3">
            <div class="worker-info">
                <h5 class="headline">Connect with thousands of workers near you</h5>
                <p>GRSP intelligently matches businesses with our vetted pool of 6 million workers whether you need them for a few hours, a few months, or anything in between.</p>
                <!-- <a href="register.php" class="btn btn-new">FIND WORK</a> -->

                <a href="./worker/workerregister.php" class="btn btn-new">FIND WORK</a>
            </div>

            <div class="galley mt-3">
                <div class="d-flex scroll">
                    <div>
                        <img src="./assets/images/galley/1.png" alt="">
                    </div>
                    <div>
                        <img src="./assets/images/galley/2.png" alt="">
                    </div>
                    <div>
                        <img src="./assets/images/galley/1.png" alt="">
                    </div>
                    <!-- <div>
                            <img src="./assets/images/galley/2.png" alt="">
                        </div> -->
                </div>
            </div>
            <div class="worker-info">
                <h5 class="headline">Struggling to find qualified and reliable workers? </h5>
                <p>It's getting harder to find hourly workers that are a fit for your business through traditional staffing agencies. Instawork's platform makes it easy to find the qualified and reliable workers you need.</p>
            </div>
            <!--  -->
            <div class="worker-info">
                <p>ECONOMIC RESEARCH </p>
                <h5 class="headline">Real time metrics on the labor market</h5>
                <p>Local labor market insights and reports that explore the trends and statistics that affect your business</p>
                <a href="about.php" class="btn btn-new">EXPLORE MORE</a>
            </div>
            <!--  -->
            <div class="bus_work">
                <div class="d-flex">
                    <button class="btn-x active-btn" id="btn-business" onclick="showBusiness()">Business</button>
                    <button class="btn-x" id="btn-work" onclick="showWork()">Work</button>
                </div>
                <div class="business" id="business">
                    <h5 class="headline">Fill your shifts in a few clicks.</h5>
                    <ul class="ul">
                        <li>1. <strong>Post a shift</strong> Enter your shift details like role, start/end time, and pay rates. Add instructions like attire or physical requirements (e g. standing, lifting).</li>
                        <li>2. Match with workers in minutes</li>
                        <li>3. Workers arrive, we do the rest</li>
                    </ul>
                </div>
                <div class="work" id="work" style="display:none;">
                    <h5 class="headline">Be your own boss. Get paid instantly.</h5>
                    <ul class="ul">
                        <li>1. Create your profile</li>
                        <li>2. Pick up a shift</li>
                        <li>3. Work the shift and get paid</li>
                        <li>4. Earn bonuses and rewards</li>
                    </ul>
                    <p>Our Top Pro Program offers cash bonuses for applicable shifts.</p>
                </div>
            </div>


        </div>
    </div>

    <!-- carousel end -->


    <div class="p-5"></div>



    <!-- footer -->
    <?php require_once('inc/footer.php'); ?>
    <!-- /footer  -->

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.0.5/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
        integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous">
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"
        integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy" crossorigin="anonymous">
    </script>
    <script>
        function showBusiness() {
            document.getElementById("business").style.display = "block";
            document.getElementById("work").style.display = "none";

            // Toggle active button
            document.getElementById("btn-business").classList.add("active-btn");
            document.getElementById("btn-work").classList.remove("active-btn");
        }

        function showWork() {
            document.getElementById("business").style.display = "none";
            document.getElementById("work").style.display = "block";

            // Toggle active button
            document.getElementById("btn-business").classList.remove("active-btn");
            document.getElementById("btn-work").classList.add("active-btn");
        }
    </script>
    <script>
        function removeScrollBarPushing() {
            const offsetY = document.documentElement.scrollTop;
            let i = 0;
            const time = setInterval(function() {
                if (i++ < 2) {
                    clearInterval(time);
                }
                document.documentElement.scrollTop = offsetY;
            }, 1);
        }

        // open sidenav
        document
            .getElementById("nav-toggle-btn")
            .addEventListener("click", function() {
                document.getElementById("sidenav").classList.add("show");
                removeScrollBarPushing();
            });
        // close sidenav
        document
            .querySelector("#sidenav .closebtn")
            .addEventListener("click", function() {
                document.getElementById("sidenav").classList.remove("show");
            });

        $(".hamburger").on("click", function() {
            $(this).parent().toggleClass("active");
        });

        function toggleSearchInput() {
            var searchInput = document.querySelector(".search-input");
            searchInput.style.display =
                searchInput.style.display === "none" ||
                searchInput.style.display === "" ?
                "block" :
                "none";
        }
    </script>
</body>

</html>