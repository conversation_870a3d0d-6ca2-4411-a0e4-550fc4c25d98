<!-- Booking Modal -->
<div class="modal fade" id="bookingModal" tabindex="-1" aria-labelledby="bookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="bookingModalLabel">
                    <i class="fa-solid fa-calendar-plus"></i> Book Service
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="bookingForm">
                    <input type="hidden" id="worker_category_id" name="worker_category_id">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="booking_date" class="form-label">
                                <i class="fa-solid fa-calendar"></i> Booking Date *
                            </label>
                            <input type="date" class="form-control" id="booking_date" name="booking_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="booking_time" class="form-label">
                                <i class="fa-solid fa-clock"></i> Booking Time *
                            </label>
                            <input type="time" class="form-control" id="booking_time" name="booking_time" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="user_address" class="form-label">
                            <i class="fa-solid fa-home"></i> Service Address *
                        </label>
                        <textarea class="form-control" id="user_address" name="user_address" rows="3" 
                                  placeholder="Enter complete address where service is required" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="user_phone" class="form-label">
                            <i class="fa-solid fa-phone"></i> Your Phone Number *
                        </label>
                        <input type="tel" class="form-control" id="user_phone" name="user_phone" 
                               placeholder="Enter your contact number" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="service_description" class="form-label">
                            <i class="fa-solid fa-sticky-note"></i> Service Description
                        </label>
                        <textarea class="form-control" id="service_description" name="service_description" rows="3" 
                                  placeholder="Describe your requirements in detail (optional)"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="total_price" class="form-label">
                            <i class="fa-solid fa-rupee-sign"></i> Estimated Price
                        </label>
                        <input type="number" class="form-control" id="total_price" name="total_price" 
                               placeholder="Leave empty for worker's standard rate" min="0" step="0.01">
                        <small class="form-text text-muted">
                            Final price will be confirmed by the worker
                        </small>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fa-solid fa-info-circle"></i> Booking Process:</h6>
                        <ul class="mb-0">
                            <li>Your booking will be sent to the worker for confirmation</li>
                            <li>Worker will contact you within 24 hours</li>
                            <li>You can track booking status in your bookings page</li>
                            <li>Payment will be made directly to the worker</li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fa-solid fa-times"></i> Cancel
                </button>
                <button type="button" class="btn btn-primary" id="submitBooking">
                    <i class="fa-solid fa-calendar-check"></i> Book Now
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    $('#booking_date').attr('min', today);
    
    // Open booking modal
    window.openBookingModal = function(workerCategoryId, workerName, categoryName, price, priceType, minHours) {
        $('#worker_category_id').val(workerCategoryId);
        $('#bookingModalLabel').html('<i class="fa-solid fa-calendar-plus"></i> Book Service - ' + workerName);
        
        // Set estimated price if available
        if (price && price > 0) {
            let estimatedPrice = price;
            if (priceType === 'hourly' && minHours && minHours > 0) {
                estimatedPrice = price * minHours;
                $('#total_price').attr('placeholder', 'Estimated: ₹' + estimatedPrice + ' (' + price + '/hr × ' + minHours + 'h minimum)');
            } else if (priceType === 'hourly') {
                $('#total_price').attr('placeholder', 'Rate: ₹' + price + '/hour');
            } else {
                $('#total_price').attr('placeholder', 'Estimated: ₹' + price);
            }
            $('#total_price').val(estimatedPrice);
        }
        
        $('#bookingModal').modal('show');
    };
    
    // Submit booking
    $('#submitBooking').click(function() {
        const form = $('#bookingForm')[0];
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        
        const submitBtn = $(this);
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fa-solid fa-spinner fa-spin"></i> Booking...').prop('disabled', true);
        
        $.ajax({
            url: 'ajax/create_booking.php',
            type: 'POST',
            data: $('#bookingForm').serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert('Success! ' + response.message);
                    $('#bookingModal').modal('hide');
                    $('#bookingForm')[0].reset();
                    
                    // Optionally redirect to bookings page
                    if (confirm('Would you like to view your bookings?')) {
                        window.location.href = 'booking.php';
                    }
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Reset form when modal is closed
    $('#bookingModal').on('hidden.bs.modal', function() {
        $('#bookingForm')[0].reset();
        $('#total_price').attr('placeholder', 'Leave empty for worker\'s standard rate');
    });
});
</script>

<style>
.modal-header.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.alert-info {
    background-color: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    color: #2c3e50;
}

.alert-info h6 {
    color: #667eea;
}
</style>
