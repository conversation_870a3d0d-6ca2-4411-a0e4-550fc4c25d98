<?php
require_once ('admin/lib/db_connection.php');
require_once ('lib/auth.php');
require_once ('worker/lib/function.php');

if(!isset($_GET['id']) && $_GET['id']=='')
{
header('Location: home.php');
}

// Handle booking submission
if(isset($_POST['book_service']))
{
    $userId = $_SESSION['user_id'];
    $workerCategoryId = $_GET['id'];

    // Get worker_id from worker_category table
    $workerQuery = dbQuery("SELECT worker_id FROM tabl_workers_category WHERE id = '$workerCategoryId'");
    $workerData = dbFetchAssoc($workerQuery);
    $workerId = $workerData['worker_id'];

    $serviceId = $_POST['service_id'];
    $bookingDate = $_POST['booking_date'];
    $bookingTime = $_POST['booking_time'];
    $serviceDescription = $_POST['service_description'];
    $userAddress = $_POST['user_address'];
    $userPhone = $_POST['user_phone'];

    // Get service price
    $serviceQuery = dbQuery("SELECT price FROM tabl_worker_services WHERE id = '$serviceId'");
    $serviceData = dbFetchAssoc($serviceQuery);
    $totalPrice = $serviceData['price'];

    $insertQuery = "INSERT INTO tabl_worker_bookings
                   (user_id, worker_category_id, worker_id, service_id, booking_date, booking_time,
                    service_description, user_address, user_phone, total_price)
                   VALUES
                   ('$userId', '$workerCategoryId', '$workerId', '$serviceId', '$bookingDate', '$bookingTime',
                    '$serviceDescription', '$userAddress', '$userPhone', '$totalPrice')";

    if(dbQuery($insertQuery))
    {
        echo "<script>
                Swal.fire({
                    title: 'Success!',
                    text: 'Booking request submitted successfully! The worker will contact you soon.',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
              </script>";
    }
    else
    {
        echo "<script>
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to submit booking. Please try again.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
              </script>";
    }
}

if(isset($_POST['addToFav']))
{
  // add in fav
  $userId = $_SESSION['user_id'];
  $workerId = $_GET['id'];
  if(dbQuery("INSERT INTO tabl_wishlist (`user_id`,`worker_id`) VALUES ('$userId','$workerId')"))
  {
    echo "<script>alert('Added In Favorite..')</script>";
  }
  else
  {
    echo "<script>alert('Faild To Add..')</script>";
  }

}
$id = $_GET['id'];

$query = dbQuery("SELECT wc.description AS description,wc.id,w.name,w.dob,wc.experience,wc.state,wc.city,w.phone,wc.worker_image,wc.cat_id FROM 
tabl_workers_category wc
INNER JOIN 
tabl_workers w ON wc.worker_id = w.worker_id
WHERE 
wc.status = 1 AND wc.id=$id");

$row = dbFetchAssoc($query);
// $profileQuery = dbQuery("SELECT * FROM tabl_workers WHERE status=1 AND worker_id=$id");
// $profile = dbFetchAssoc($profileQuery);

// $query = dbQuery("SELECT * FROM tabl_workers_category WHERE cat_status=1 AND id = $id");
// $row = dbFetchAssoc($query);

$catQuery = dbQuery("SELECT cat_name FROM tabl_categories WHERE cat_id=".$row['cat_id']);
$catRun = dbFetchAssoc($catQuery);

// getting user details 
$userQurry = dbQuery("SELECT fullname,phone_number FROM tabl_users WHERE user_id=".$_SESSION['user_id']);
$userRun = dbFetchAssoc($userQurry);
$user_details = "User Details\n".$userRun['fullname']."\n".$userRun['phone_number'];
$worker_details = "Worker Details :\n".$row['name']." (".$catRun['cat_name'].")\n".$row['phone']."\n".$row['city']."\nWorker id: ".$row['id']."";

// URL encode the message
$message = urlencode("$user_details\n\n$worker_details");

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous" />
    <!--  -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!--  -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baloo+Bhai+2:wght@400..800&display=swap" rel="stylesheet">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Modern Booking Styles -->
    <link rel="stylesheet" href="assets/css/booking-styles.css">
    <style>
    @import url("https://fonts.googleapis.com/css?family=Quicksand:400,500,700&subset=latin-ext");

    html {
        position: relative;
        overflow-x: hidden !important;
    }

    * {
        box-sizing: border-box;
    }

    body {
        font-family: "Quicksand", sans-serif;
        color: #324e63;
        background-color: #182b3a;
        background-image: linear-gradient(315deg, #182b3a 0%, #1230b4 74%);
    }

    a,
    a:hover {
        text-decoration: none;
    }

    .icon {
        display: inline-block;
        width: 1em;
        height: 1em;
        stroke-width: 0;
        stroke: currentColor;
        fill: currentColor;
    }

    .wrapper {
        width: 100%;

        padding: 50px 20px;
        padding-top: 100px;
        display: flex;
        /* background-image: linear-gradient(-20deg, #ff2846 0%, #6944ff 100%); */
        display: flex;
        background-color: #182b3a;
        /* background-image: linear-gradient(315deg, #182b3a 0%, #1230b4 74%);
     */
        background-color: transparent;
    }

    @media screen and (max-width: 768px) {
        .wrapper {
            height: auto;
            min-height: 100vh;
            padding-top: 100px;
        }
    }

    .profile-card {
        width: 100%;
        min-height: 460px;
        margin: auto;
        box-shadow: 0px 8px 60px -10px rgba(13, 28, 39, 0.6);
        background: #fff;
        border-radius: 12px;
        max-width: 800px;
        position: relative;
    }

    .profile-card.active .profile-card__cnt {
        filter: blur(6px);
    }

    .profile-card.active .profile-card-message,
    .profile-card.active .profile-card__overlay {
        opacity: 1;
        pointer-events: auto;
        transition-delay: 0.1s;
    }

    .profile-card.active .profile-card-form {
        transform: none;
        transition-delay: 0.1s;
    }

    .profile-card__img {
        width: 150px;
        height: 150px;
        margin-left: auto;
        margin-right: auto;
        transform: translateY(-50%);
        border-radius: 50%;
        overflow: hidden;
        position: relative;
        z-index: 4;
        box-shadow: 0px 5px 50px 0px #6c44fc, 0px 0px 0px 7px rgba(107, 74, 255, 0.5);
    }

    @media screen and (max-width: 576px) {
        .profile-card__img {
            width: 120px;
            height: 120px;
        }
    }

    .profile-card__img img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
        background-color: #fff;
    }

    .profile-card__cnt {
        margin-top: -35px;
        text-align: center;
        padding: 0 20px;
        padding-bottom: 40px;
        transition: all 0.3s;
    }

    .profile-card__name {
        font-weight: 700;
        font-size: 24px;
        color: #6944ff;
        margin-bottom: 2px;
    }

    .profile-card__txt {
        font-size: 18px;
        font-weight: 500;
        color: #324e63;
        margin-bottom: 15px;
    }

    .profile-card__txt strong {
        font-weight: 700;
    }

    .profile-card-loc {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        font-weight: 600;
    }

    .profile-card-loc__icon {
        display: inline-flex;
        font-size: 27px;
        margin-right: 10px;
    }

    .profile-card-inf {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        align-items: flex-start;
        margin-top: 35px;
    }

    .profile-card-inf__item {
        padding: 10px 35px;
        min-width: 150px;
    }

    @media screen and (max-width: 768px) {
        .profile-card-inf__item {
            padding: 10px 20px;
            min-width: 120px;
        }
    }

    .profile-card-inf__title {
        font-weight: 700;
        font-size: 27px;
        color: #324e63;
    }

    .profile-card-inf__txt {
        font-weight: 500;
        margin-top: 7px;
    }

    .profile-card-social {
        margin-top: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }

    .profile-card-social__item {
        display: inline-flex;
        width: 55px;
        height: 55px;
        margin: 15px;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        color: #fff;
        background: #405de6;
        box-shadow: 0px 7px 30px rgba(43, 98, 169, 0.5);
        position: relative;
        font-size: 21px;
        flex-shrink: 0;
        transition: all 0.3s;
    }

    @media screen and (max-width: 768px) {
        .profile-card-social__item {
            width: 50px;
            height: 50px;
            margin: 10px;
        }
    }

    @media screen and (min-width: 768px) {
        .profile-card-social__item:hover {
            transform: scale(1.2);
        }
    }

    .profile-card-social__item.facebook {
        background: linear-gradient(45deg, #3b5998, #0078d7);
        box-shadow: 0px 4px 30px rgba(43, 98, 169, 0.5);
    }

    .profile-card-social__item.twitter {
        background: linear-gradient(45deg, #1da1f2, #0e71c8);
        box-shadow: 0px 4px 30px rgba(19, 127, 212, 0.7);
    }

    .profile-card-social__item.instagram {
        background: linear-gradient(45deg, #405de6, #5851db, #833ab4, #c13584, #e1306c, #fd1d1d);
        box-shadow: 0px 4px 30px rgba(120, 64, 190, 0.6);
    }

    .profile-card-social__item.behance {
        background: linear-gradient(45deg, #1769ff, #213fca);
        box-shadow: 0px 4px 30px rgba(27, 86, 231, 0.7);
    }

    .profile-card-social__item.github {
        background: linear-gradient(45deg, #333333, #626b73);
        box-shadow: 0px 4px 30px rgba(63, 65, 67, 0.6);
    }

    .profile-card-social__item.codepen {
        background: linear-gradient(45deg, #324e63, #414447);
        box-shadow: 0px 4px 30px rgba(55, 75, 90, 0.6);
    }

    .profile-card-social__item.link {
        background: linear-gradient(45deg, #d5135a, #f05924);
        box-shadow: 0px 4px 30px rgba(223, 45, 70, 0.6);
    }

    .profile-card-social .icon-font {
        display: inline-flex;
    }

    .profile-card-ctr {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40px;
    }

    @media screen and (max-width: 576px) {
        .profile-card-ctr {
            flex-wrap: wrap;
        }
    }

    .profile-card__button {
        background: none;
        border: none;
        font-family: "Quicksand", sans-serif;
        font-weight: 700;
        font-size: 16px;
        margin: 15px 35px;
        padding: 2px 16px;
        min-width: 201px;
        border-radius: 50px;
        min-height: 49px;
        color: #fff;
        cursor: pointer;
        backface-visibility: hidden;
        transition: all 0.3s;
    }

    @media screen and (max-width: 768px) {
        .profile-card__button {
            min-width: 170px;
            margin: 15px 25px;
        }
    }

    @media screen and (max-width: 576px) {
        .profile-card__button {
            min-width: inherit;
            margin: 0;
            margin-bottom: 0px;
            width: 100%;
            max-width: 135px;
        }

        .profile-card__button:last-child {
            margin-bottom: 0;
        }
    }

    .profile-card__button:focus {
        outline: none !important;
    }

    @media screen and (min-width: 768px) {
        .profile-card__button:hover {
            transform: translateY(-5px);
        }
    }

    .profile-card__button:first-child {
        margin-left: 0;
    }

    .profile-card__button:last-child {
        margin-right: 0;
    }

    .profile-card__button.button--blue {
        background: linear-gradient(45deg, #1da1f2, #0e71c8);
        box-shadow: 0px 4px 30px rgba(19, 127, 212, 0.4);
    }

    .profile-card__button.button--blue:hover {
        box-shadow: 0px 7px 30px rgba(19, 127, 212, 0.75);
    }

    .profile-card__button.button--orange {
        background: linear-gradient(45deg, #0fac16, #134f1d);
        box-shadow: 0px 4px 30px rgba(223, 45, 70, 0.35);
    }

    .profile-card__button.button--orange:hover {
        box-shadow: 0px 7px 30px rgba(223, 45, 70, 0.75);
    }

    .profile-card__button.button--gray {
        box-shadow: none;
        background: #dcdcdc;
        color: #142029;
    }

    .profile-card-message {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        padding-top: 130px;
        padding-bottom: 100px;
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s;
    }

    .profile-card-form {
        box-shadow: 0 4px 30px rgba(15, 22, 56, 0.35);
        max-width: 80%;
        margin-left: auto;
        margin-right: auto;
        height: 100%;
        background: #fff;
        border-radius: 10px;
        padding: 35px;
        transform: scale(0.8);
        position: relative;
        z-index: 3;
        transition: all 0.3s;
    }

    @media screen and (max-width: 768px) {
        .profile-card-form {
            max-width: 90%;
            height: auto;
        }
    }

    @media screen and (max-width: 576px) {
        .profile-card-form {
            padding: 20px;
        }
    }

    .profile-card-form__bottom {
        justify-content: space-between;
        display: flex;
    }

    @media screen and (max-width: 576px) {
        .profile-card-form__bottom {
            flex-wrap: wrap;
        }
    }

    .profile-card textarea {
        width: 100%;
        resize: none;
        height: 210px;
        margin-bottom: 20px;
        border: 2px solid #dcdcdc;
        border-radius: 10px;
        padding: 15px 20px;
        color: #324e63;
        font-weight: 500;
        font-family: "Quicksand", sans-serif;
        outline: none;
        transition: all 0.3s;
    }

    .profile-card textarea:focus {
        outline: none;
        border-color: #8a979e;
    }

    .profile-card__overlay {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;
        opacity: 0;
        background: rgba(22, 33, 72, 0.35);
        border-radius: 12px;
        transition: all 0.3s;
    }

    .rating {
        font-size: 24px;
    }

    .rating .star {
        cursor: pointer;
        color: #ccc;
    }

    .rating .star.active {
        color: #FFD700;
        /* Change color for active stars */
    }

    /* Services Section Styles */
    .services-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px;
        margin-top: 20px;
        color: white;
    }

    .services-section h4 {
        color: white;
        font-weight: 700;
        margin-bottom: 30px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .service-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
        cursor: pointer;
        color: #333;
    }

    .service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    }

    .service-card.selected {
        border: 3px solid #28a745;
        transform: translateY(-5px);
    }

    .service-image {
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    .service-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .service-card:hover .service-image img {
        transform: scale(1.1);
    }

    .service-info {
        padding: 20px;
    }

    .service-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .service-description {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin-bottom: 15px;
        line-height: 1.5;
    }

    .service-price {
        font-size: 1.5rem;
        font-weight: 700;
        color: #e74c3c;
        margin-bottom: 15px;
    }

    .btn-select-service {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        width: 100%;
        transition: all 0.3s ease;
    }

    .btn-select-service:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .service-card.selected .btn-select-service {
        background: #28a745;
    }

    /* Booking Form Styles */
    .booking-section {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 15px;
        padding: 30px;
        margin-top: 20px;
        color: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .booking-section h4 {
        color: white;
        font-weight: 700;
        margin-bottom: 30px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .selected-service-info {
        background: rgba(255,255,255,0.2);
        border-radius: 10px;
        padding: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.3);
    }

    .booking-section .form-label {
        font-weight: 600;
        color: white;
        margin-bottom: 8px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .booking-section .form-control {
        border-radius: 10px;
        border: 2px solid rgba(255,255,255,0.3);
        padding: 12px 15px;
        background: rgba(255,255,255,0.9);
        color: #333;
        font-weight: 500;
    }

    .booking-section .form-control:focus {
        border-color: #fff;
        box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.3);
        background: white;
    }

    .booking-summary {
        background: rgba(255,255,255,0.2);
        border-radius: 10px;
        padding: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.3);
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .summary-item:last-child {
        margin-bottom: 0;
        font-size: 1.2rem;
        border-top: 1px solid rgba(255,255,255,0.3);
        padding-top: 10px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 700;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    }

    .btn-secondary {
        background: rgba(255,255,255,0.2);
        border: 2px solid rgba(255,255,255,0.5);
        color: white;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 700;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: rgba(255,255,255,0.3);
        border-color: white;
        color: white;
    }
    </style>
</head>

<body>
    <div class="wrapper">


        <div class="profile-card js-profile-card">
            <div class="profile-card__img">
                <?php
          $profile_img = $row['worker_image'];
          $imageSrc = '';
          if (file_exists('worker/assets/images/workers/workers-img/' . $profile_img)){
            $imageSrc = 'worker/assets/images/workers/workers-img/' . $profile_img;
          }else{
            $imageSrc = 'assets/images/usericon.png';
          }
        ?>
                <img src="<?php echo $imageSrc; ?>" onerror="this.src=`assets/images/profile.jpg`" alt="profile-img">
            </div>

            <div class="profile-card__cnt js-profile-cnt">
                <div class="profile-card__name"><?=$row['name']?></div>
                <div class="profile-card__txt"> Age:<?php echo calculateAge($row['dob']); ?> Years</div>
                <div class="profile-card__txt"> Experience: <?=isset($row['experience'])?$row['experience']:''; ?> Years</div>
                <div class="profile-card__txt"> Skills: <?=isset($row['description'])?$row['description']:''; ?></div>
                <div class="profile-card-loc">
                    <span class="profile-card-loc__icon">
                        <svg class="icon">
                            <use xlink:href="#icon-location"></use>
                        </svg>
                    </span>

                    <span class="profile-card-loc__txt">
                        <?=$row['city']?><?=isset($row['state'])?','.getState($row['state']):''?>
                    </span>
                </div>

                <hr>

                <!-- Available Services Section -->
                <div class="services-section mt-4 gradient-purple">
                    <h4 class="text-center mb-4 floating">
                        <i class="fa-solid fa-briefcase"></i> Available Services
                    </h4>

                    <?php
                    // Get worker_id from worker_category table
                    $workerQuery = dbQuery("SELECT worker_id FROM tabl_workers_category WHERE id = '".$_GET['id']."'");
                    $workerData = dbFetchAssoc($workerQuery);
                    $workerId = $workerData['worker_id'];

                    // Get worker's services
                    $servicesQuery = dbQuery("SELECT * FROM tabl_worker_services WHERE worker_id = '$workerId' AND status = 1 ORDER BY service_name ASC");

                    if(mysqli_num_rows($servicesQuery) > 0):
                    ?>
                    <div class="services-grid">
                        <?php while($service = dbFetchAssoc($servicesQuery)): ?>
                        <div class="service-card modern-card" data-service-id="<?=$service['id']?>" data-price="<?=$service['price']?>">
                            <div class="service-image">
                                <img src="worker/assets/images/workers_services/<?=$service['image']?>"
                                     alt="<?=$service['service_name']?>"
                                     onerror="this.src='assets/images/default-service.jpg'">
                            </div>
                            <div class="service-info">
                                <h5 class="service-name"><?=$service['service_name']?></h5>
                                <p class="service-description"><?=$service['servicesInclude']?></p>
                                <div class="price-display">₹<?=$service['price']?></div>
                                <button type="button" class="btn-select-service btn-pulse" onclick="selectService(<?=$service['id']?>, '<?=$service['service_name']?>', <?=$service['price']?>)">
                                    <i class="fa-solid fa-plus"></i> Select Service
                                </button>
                            </div>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info text-center">
                        <i class="fa-solid fa-info-circle"></i>
                        No services available for this worker at the moment.
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Booking Section -->
                <div class="booking-section mt-4 gradient-pink" id="bookingSection" style="display: none;">
                    <h4 class="text-center mb-4 floating">
                        <i class="fa-solid fa-calendar-check"></i> Book Your Service
                    </h4>

                    <div class="selected-service-info glass-card mb-4" id="selectedServiceInfo">
                        <!-- Selected service info will be displayed here -->
                    </div>

                    <form id="bookingForm" method="post">
                        <input type="hidden" id="service_id" name="service_id" required>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="booking_date" class="form-label">
                                    <i class="fa-solid fa-calendar"></i> Preferred Date
                                </label>
                                <input type="date" class="modern-form-control" id="booking_date" name="booking_date" required min="<?=date('Y-m-d')?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="booking_time" class="form-label">
                                    <i class="fa-solid fa-clock"></i> Preferred Time
                                </label>
                                <input type="time" class="modern-form-control" id="booking_time" name="booking_time" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="service_description" class="form-label">
                                <i class="fa-solid fa-comment"></i> Additional Requirements
                            </label>
                            <textarea class="modern-form-control" id="service_description" name="service_description" rows="3" placeholder="Any specific requirements or notes..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="user_address" class="form-label">
                                <i class="fa-solid fa-location-dot"></i> Service Address
                            </label>
                            <textarea class="modern-form-control" id="user_address" name="user_address" rows="2" placeholder="Enter complete address where service is needed..." required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="user_phone" class="form-label">
                                <i class="fa-solid fa-phone"></i> Contact Number
                            </label>
                            <input type="tel" class="modern-form-control" id="user_phone" name="user_phone" placeholder="Your phone number" required>
                        </div>

                        <div class="booking-summary glass-card mb-4">
                            <div class="summary-item">
                                <span>Service:</span>
                                <span id="summaryServiceName">-</span>
                            </div>
                            <div class="summary-item">
                                <span>Price:</span>
                                <span id="summaryPrice" class="price-display">₹0</span>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="button" class="btn btn-secondary me-2" onclick="cancelBooking()">
                                <i class="fa-solid fa-times"></i> Cancel
                            </button>
                            <button type="submit" name="book_service" class="btn btn-primary btn-lg">
                                <i class="fa-solid fa-calendar-check"></i> Confirm Booking
                            </button>
                        </div>
                    </form>
                </div>
<?php
if(mysqli_num_rows(dbQuery("SELECT * FROM tabl_wishlist WHERE user_id=".$_SESSION['user_id']." AND worker_id=".$_GET['id'])))
{
  ?>
 <button class="addToFav btn btn-success my-2">Your Favorite</button>
                </form>
  <?php
}
else
{
?>
                <form method="post">
                <button class="addToFav btn btn-danger my-2" type="submit" name="addToFav" >Add To Favorite</button>
                </form>
                <?php
}
?>
                <div id="ratings">
                    <?php
                    // Check if user has completed bookings with this worker to allow rating
                    $userId = $_SESSION['user_id'];
                    $workerCategoryId = $_GET['id'];
                    $completedBookingsQuery = dbQuery("SELECT COUNT(*) as count FROM tabl_worker_bookings
                                                      WHERE user_id = '$userId'
                                                      AND worker_category_id = '$workerCategoryId'
                                                      AND status = 2");
                    $completedBookings = dbFetchAssoc($completedBookingsQuery);

                    if($completedBookings['count'] > 0):
                    ?>
                    <div id="add-rating">
                        <h3>Add Rating</h3>
                        <p class="text-muted">You can rate this worker because you have completed a service with them.</p>
                        <div class="rating">
                            <span class="star" data-value="1">★</span>
                            <span class="star" data-value="2">★</span>
                            <span class="star" data-value="3">★</span>
                            <span class="star" data-value="4">★</span>
                            <span class="star" data-value="5">★</span>
                        </div>
                        <textarea id="comment" class="form-control" rows="3" placeholder="Add comment about your experience"></textarea>
                        <button id="submit-rating" class="btn btn-primary mt-2">Submit Rating</button>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info">
                        <h5>Rating System</h5>
                        <p>You can rate this worker after completing a service booking with them.</p>
                    </div>
                    <?php endif; ?>

                    <div id="ratings-list" style="text-align:left;">
                        <!-- Ratings will be dynamically loaded here -->
                    </div>
                </div>

            </div>



        </div>

    </div>
    <svg hidden="hidden">
        <defs>
            <symbol id="icon-codepen" viewBox="0 0 32 32">
                <title>codepen</title>
                <path
                    d="M31.872 10.912v-0.032c0-0.064 0-0.064 0-0.096v-0.064c0-0.064 0-0.064-0.064-0.096 0 0 0-0.064-0.064-0.064 0-0.064-0.064-0.064-0.064-0.096 0 0 0-0.064-0.064-0.064 0-0.064-0.064-0.064-0.064-0.096l-0.192-0.192v-0.064l-0.064-0.064-14.592-9.696c-0.448-0.32-1.056-0.32-1.536 0l-14.528 9.696-0.32 0.32c0 0-0.064 0.064-0.064 0.096 0 0 0 0.064-0.064 0.064 0 0.064-0.064 0.064-0.064 0.096 0 0 0 0.064-0.064 0.064 0 0.064 0 0.064-0.064 0.096v0.064c0 0.064 0 0.064 0 0.096v0.064c0 0.064 0 0.096 0 0.16v9.696c0 0.064 0 0.096 0 0.16v0.064c0 0.064 0 0.064 0 0.096v0.064c0 0.064 0 0.064 0.064 0.096 0 0 0 0.064 0.064 0.064 0 0.064 0.064 0.064 0.064 0.096 0 0 0 0.064 0.064 0.064 0 0.064 0.064 0.064 0.064 0.096l0.256 0.256 0.064 0.032 14.528 9.728c0.224 0.16 0.48 0.224 0.768 0.224s0.544-0.064 0.768-0.224l14.528-9.728 0.32-0.32c0 0 0.064-0.064 0.064-0.096 0 0 0-0.064 0.064-0.064 0-0.064 0.064-0.064 0.064-0.096 0 0 0-0.064 0.064-0.064 0-0.064 0-0.064 0.064-0.096v-0.032c0-0.064 0-0.064 0-0.096v-0.064c0-0.064 0-0.096 0-0.16v-9.664c0-0.064 0-0.096 0-0.224zM17.312 4l10.688 7.136-4.768 3.168-5.92-3.936v-6.368zM14.56 4v6.368l-5.92 3.968-4.768-3.168 10.688-7.168zM2.784 13.664l3.392 2.304-3.392 2.304c0 0 0-4.608 0-4.608zM14.56 28l-10.688-7.136 4.768-3.2 5.92 3.936v6.4zM15.936 19.2l-4.832-3.232 4.832-3.232 4.832 3.232-4.832 3.232zM17.312 28v-6.432l5.92-3.936 4.8 3.168-10.72 7.2zM29.12 18.272l-3.392-2.304 3.392-2.304v4.608z">
                </path>
            </symbol>

            <symbol id="icon-github" viewBox="0 0 32 32">
                <title>github</title>
                <path
                    d="M16.192 0.512c-8.832 0-16 7.168-16 16 0 7.072 4.576 13.056 10.944 15.168 0.8 0.16 1.088-0.352 1.088-0.768 0-0.384 0-1.632-0.032-2.976-4.448 0.96-5.376-1.888-5.376-1.888-0.736-1.856-1.792-2.336-1.792-2.336-1.44-0.992 0.096-0.96 0.096-0.96 1.6 0.128 2.464 1.664 2.464 1.664 1.44 2.432 3.744 1.728 4.672 1.344 0.128-1.024 0.544-1.728 1.024-2.144-3.552-0.448-7.296-1.824-7.296-7.936 0-1.76 0.64-3.168 1.664-4.288-0.16-0.416-0.704-2.016 0.16-4.224 0 0 1.344-0.416 4.416 1.632 1.28-0.352 2.656-0.544 4-0.544s2.72 0.192 4 0.544c3.040-2.080 4.384-1.632 4.384-1.632 0.864 2.208 0.32 3.84 0.16 4.224 1.024 1.12 1.632 2.56 1.632 4.288 0 6.144-3.744 7.488-7.296 7.904 0.576 0.512 1.088 1.472 1.088 2.976 0 2.144-0.032 3.872-0.032 4.384 0 0.416 0.288 0.928 1.088 0.768 6.368-2.112 10.944-8.128 10.944-15.168 0-8.896-7.168-16.032-16-16.032z">
                </path>
                <path
                    d="M6.24 23.488c-0.032 0.064-0.16 0.096-0.288 0.064-0.128-0.064-0.192-0.16-0.128-0.256 0.032-0.096 0.16-0.096 0.288-0.064 0.128 0.064 0.192 0.16 0.128 0.256v0z">
                </path>
                <path
                    d="M6.912 24.192c-0.064 0.064-0.224 0.032-0.32-0.064s-0.128-0.256-0.032-0.32c0.064-0.064 0.224-0.032 0.32 0.064s0.096 0.256 0.032 0.32v0z">
                </path>
                <path
                    d="M7.52 25.12c-0.096 0.064-0.256 0-0.352-0.128s-0.096-0.32 0-0.384c0.096-0.064 0.256 0 0.352 0.128 0.128 0.128 0.128 0.32 0 0.384v0z">
                </path>
                <path
                    d="M8.384 26.016c-0.096 0.096-0.288 0.064-0.416-0.064s-0.192-0.32-0.096-0.416c0.096-0.096 0.288-0.064 0.416 0.064 0.16 0.128 0.192 0.32 0.096 0.416v0z">
                </path>
                <path
                    d="M9.6 26.528c-0.032 0.128-0.224 0.192-0.384 0.128-0.192-0.064-0.288-0.192-0.256-0.32s0.224-0.192 0.416-0.128c0.128 0.032 0.256 0.192 0.224 0.32v0z">
                </path>
                <path
                    d="M10.912 26.624c0 0.128-0.16 0.256-0.352 0.256s-0.352-0.096-0.352-0.224c0-0.128 0.16-0.256 0.352-0.256 0.192-0.032 0.352 0.096 0.352 0.224v0z">
                </path>
                <path
                    d="M12.128 26.4c0.032 0.128-0.096 0.256-0.288 0.288s-0.352-0.032-0.384-0.16c-0.032-0.128 0.096-0.256 0.288-0.288s0.352 0.032 0.384 0.16v0z">
                </path>
            </symbol>

            <symbol id="icon-location" viewBox="0 0 32 32">
                <title>location</title>
                <path
                    d="M16 31.68c-0.352 0-0.672-0.064-1.024-0.16-0.8-0.256-1.44-0.832-1.824-1.6l-6.784-13.632c-1.664-3.36-1.568-7.328 0.32-10.592 1.856-3.2 4.992-5.152 8.608-5.376h1.376c3.648 0.224 6.752 2.176 8.608 5.376 1.888 3.264 2.016 7.232 0.352 10.592l-6.816 13.664c-0.288 0.608-0.8 1.12-1.408 1.408-0.448 0.224-0.928 0.32-1.408 0.32zM15.392 2.368c-2.88 0.192-5.408 1.76-6.912 4.352-1.536 2.688-1.632 5.92-0.288 8.672l6.816 13.632c0.128 0.256 0.352 0.448 0.64 0.544s0.576 0.064 0.832-0.064c0.224-0.096 0.384-0.288 0.48-0.48l6.816-13.664c1.376-2.752 1.248-5.984-0.288-8.672-1.472-2.56-4-4.128-6.88-4.32h-1.216zM16 17.888c-3.264 0-5.92-2.656-5.92-5.92 0-3.232 2.656-5.888 5.92-5.888s5.92 2.656 5.92 5.92c0 3.264-2.656 5.888-5.92 5.888zM16 8.128c-2.144 0-3.872 1.728-3.872 3.872s1.728 3.872 3.872 3.872 3.872-1.728 3.872-3.872c0-2.144-1.76-3.872-3.872-3.872z">
                </path>
                <path
                    d="M16 32c-0.384 0-0.736-0.064-1.12-0.192-0.864-0.288-1.568-0.928-1.984-1.728l-6.784-13.664c-1.728-3.456-1.6-7.52 0.352-10.912 1.888-3.264 5.088-5.28 8.832-5.504h1.376c3.744 0.224 6.976 2.24 8.864 5.536 1.952 3.36 2.080 7.424 0.352 10.912l-6.784 13.632c-0.32 0.672-0.896 1.216-1.568 1.568-0.48 0.224-0.992 0.352-1.536 0.352zM15.36 0.64h-0.064c-3.488 0.224-6.56 2.112-8.32 5.216-1.824 3.168-1.952 7.040-0.32 10.304l6.816 13.632c0.32 0.672 0.928 1.184 1.632 1.44s1.472 0.192 2.176-0.16c0.544-0.288 1.024-0.736 1.28-1.28l6.816-13.632c1.632-3.264 1.504-7.136-0.32-10.304-1.824-3.104-4.864-5.024-8.384-5.216h-1.312zM16 29.952c-0.16 0-0.32-0.032-0.448-0.064-0.352-0.128-0.64-0.384-0.8-0.704l-6.816-13.664c-1.408-2.848-1.312-6.176 0.288-8.96 1.536-2.656 4.16-4.32 7.168-4.512h1.216c3.040 0.192 5.632 1.824 7.2 4.512 1.6 2.752 1.696 6.112 0.288 8.96l-6.848 13.632c-0.128 0.288-0.352 0.512-0.64 0.64-0.192 0.096-0.384 0.16-0.608 0.16zM15.424 2.688c-2.784 0.192-5.216 1.696-6.656 4.192-1.504 2.592-1.6 5.696-0.256 8.352l6.816 13.632c0.096 0.192 0.256 0.32 0.448 0.384s0.416 0.064 0.608-0.032c0.16-0.064 0.288-0.192 0.352-0.352l6.816-13.664c1.312-2.656 1.216-5.792-0.288-8.352-1.472-2.464-3.904-4-6.688-4.16h-1.152zM16 18.208c-3.424 0-6.24-2.784-6.24-6.24 0-3.424 2.816-6.208 6.24-6.208s6.24 2.784 6.24 6.24c0 3.424-2.816 6.208-6.24 6.208zM16 6.4c-3.072 0-5.6 2.496-5.6 5.6 0 3.072 2.528 5.6 5.6 5.6s5.6-2.496 5.6-5.6c0-3.104-2.528-5.6-5.6-5.6zM16 16.16c-2.304 0-4.16-1.888-4.16-4.16s1.888-4.16 4.16-4.16c2.304 0 4.16 1.888 4.16 4.16s-1.856 4.16-4.16 4.16zM16 8.448c-1.952 0-3.552 1.6-3.552 3.552s1.6 3.552 3.552 3.552c1.952 0 3.552-1.6 3.552-3.552s-1.6-3.552-3.552-3.552z">
                </path>
            </symbol>

            <symbol id="icon-facebook" viewBox="0 0 32 32">
                <title>facebook</title>
                <path d="M19 6h5v-6h-5c-3.86 0-7 3.14-7 7v3h-4v6h4v16h6v-16h5l1-6h-6v-3c0-0.542 0.458-1 1-1z"></path>
            </symbol>

            <symbol id="icon-instagram" viewBox="0 0 32 32">
                <title>instagram</title>
                <path
                    d="M16 2.881c4.275 0 4.781 0.019 6.462 0.094 1.563 0.069 2.406 0.331 2.969 0.55 0.744 0.288 1.281 0.638 1.837 1.194 0.563 0.563 0.906 1.094 1.2 1.838 0.219 0.563 0.481 1.412 0.55 2.969 0.075 1.688 0.094 2.194 0.094 6.463s-0.019 4.781-0.094 6.463c-0.069 1.563-0.331 2.406-0.55 2.969-0.288 0.744-0.637 1.281-1.194 1.837-0.563 0.563-1.094 0.906-1.837 1.2-0.563 0.219-1.413 0.481-2.969 0.55-1.688 0.075-2.194 0.094-6.463 0.094s-4.781-0.019-6.463-0.094c-1.563-0.069-2.406-0.331-2.969-0.55-0.744-0.288-1.281-0.637-1.838-1.194-0.563-0.563-0.906-1.094-1.2-1.837-0.219-0.563-0.481-1.413-0.55-2.969-0.075-1.688-0.094-2.194-0.094-6.463s0.019-4.781 0.094-6.463c0.069-1.563 0.331-2.406 0.55-2.969 0.288-0.744 0.638-1.281 1.194-1.838 0.563-0.563 1.094-0.906 1.838-1.2 0.563-0.219 1.412-0.481 2.969-0.55 1.681-0.075 2.188-0.094 6.463-0.094zM16 0c-4.344 0-4.887 0.019-6.594 0.094-1.7 0.075-2.869 0.35-3.881 0.744-1.056 0.412-1.95 0.956-2.837 1.85-0.894 0.888-1.438 1.781-1.85 2.831-0.394 1.019-0.669 2.181-0.744 3.881-0.075 1.713-0.094 2.256-0.094 6.6s0.019 4.887 0.094 6.594c0.075 1.7 0.35 2.869 0.744 3.881 0.413 1.056 0.956 1.95 1.85 2.837 0.887 0.887 1.781 1.438 2.831 1.844 1.019 0.394 2.181 0.669 3.881 0.744 1.706 0.075 2.25 0.094 6.594 0.094s4.888-0.019 6.594-0.094c1.7-0.075 2.869-0.35 3.881-0.744 1.050-0.406 1.944-0.956 2.831-1.844s1.438-1.781 1.844-2.831c0.394-1.019 0.669-2.181 0.744-3.881 0.075-1.706 0.094-2.25 0.094-6.594s-0.019-4.887-0.094-6.594c-0.075-1.7-0.35-2.869-0.744-3.881-0.394-1.063-0.938-1.956-1.831-2.844-0.887-0.887-1.781-1.438-2.831-1.844-1.019-0.394-2.181-0.669-3.881-0.744-1.712-0.081-2.256-0.1-6.6-0.1v0z">
                </path>
                <path
                    d="M16 7.781c-4.537 0-8.219 3.681-8.219 8.219s3.681 8.219 8.219 8.219 8.219-3.681 8.219-8.219c0-4.537-3.681-8.219-8.219-8.219zM16 21.331c-2.944 0-5.331-2.387-5.331-5.331s2.387-5.331 5.331-5.331c2.944 0 5.331 2.387 5.331 5.331s-2.387 5.331-5.331 5.331z">
                </path>
                <path
                    d="M26.462 7.456c0 1.060-0.859 1.919-1.919 1.919s-1.919-0.859-1.919-1.919c0-1.060 0.859-1.919 1.919-1.919s1.919 0.859 1.919 1.919z">
                </path>
            </symbol>

            <symbol id="icon-twitter" viewBox="0 0 32 32">
                <title>twitter</title>
                <path
                    d="M32 7.075c-1.175 0.525-2.444 0.875-3.769 1.031 1.356-0.813 2.394-2.1 2.887-3.631-1.269 0.75-2.675 1.3-4.169 1.594-1.2-1.275-2.906-2.069-4.794-2.069-3.625 0-6.563 2.938-6.563 6.563 0 0.512 0.056 1.012 0.169 1.494-5.456-0.275-10.294-2.888-13.531-6.862-0.563 0.969-0.887 2.1-0.887 3.3 0 2.275 1.156 4.287 2.919 5.463-1.075-0.031-2.087-0.331-2.975-0.819 0 0.025 0 0.056 0 0.081 0 3.181 2.263 5.838 5.269 6.437-0.55 0.15-1.131 0.231-1.731 0.231-0.425 0-0.831-0.044-1.237-0.119 0.838 2.606 3.263 4.506 6.131 4.563-2.25 1.762-5.075 2.813-8.156 2.813-0.531 0-1.050-0.031-1.569-0.094 2.913 1.869 6.362 2.95 10.069 2.95 12.075 0 18.681-10.006 18.681-18.681 0-0.287-0.006-0.569-0.019-0.85 1.281-0.919 2.394-2.075 3.275-3.394z">
                </path>
            </symbol>

            <symbol id="icon-behance" viewBox="0 0 32 32">
                <title>behance</title>
                <path
                    d="M9.281 6.412c0.944 0 1.794 0.081 2.569 0.25 0.775 0.162 1.431 0.438 1.988 0.813 0.55 0.375 0.975 0.875 1.287 1.5 0.3 0.619 0.45 1.394 0.45 2.313 0 0.994-0.225 1.819-0.675 2.481-0.456 0.662-1.119 1.2-2.006 1.625 1.213 0.35 2.106 0.962 2.706 1.831 0.6 0.875 0.887 1.925 0.887 3.163 0 1-0.194 1.856-0.575 2.581-0.387 0.731-0.912 1.325-1.556 1.781-0.65 0.462-1.4 0.8-2.237 1.019-0.831 0.219-1.688 0.331-2.575 0.331h-9.544v-19.688h9.281zM8.719 14.363c0.769 0 1.406-0.181 1.906-0.55 0.5-0.363 0.738-0.963 0.738-1.787 0-0.456-0.081-0.838-0.244-1.131-0.169-0.294-0.387-0.525-0.669-0.688-0.275-0.169-0.588-0.281-0.956-0.344-0.356-0.069-0.731-0.1-1.113-0.1h-4.050v4.6h4.388zM8.956 22.744c0.425 0 0.831-0.038 1.213-0.125 0.387-0.087 0.731-0.219 1.019-0.419 0.287-0.194 0.531-0.45 0.706-0.788 0.175-0.331 0.256-0.756 0.256-1.275 0-1.012-0.287-1.738-0.856-2.175-0.569-0.431-1.325-0.644-2.262-0.644h-4.7v5.419h4.625z">
                </path>
                <path
                    d="M22.663 22.675c0.587 0.575 1.431 0.863 2.531 0.863 0.788 0 1.475-0.2 2.044-0.6s0.913-0.825 1.044-1.262h3.45c-0.556 1.719-1.394 2.938-2.544 3.675-1.131 0.738-2.519 1.113-4.125 1.113-1.125 0-2.131-0.181-3.038-0.538-0.906-0.363-1.663-0.869-2.3-1.531-0.619-0.663-1.106-1.45-1.45-2.375-0.337-0.919-0.512-1.938-0.512-3.038 0-1.069 0.175-2.063 0.525-2.981 0.356-0.925 0.844-1.719 1.494-2.387s1.413-1.2 2.313-1.588c0.894-0.387 1.881-0.581 2.975-0.581 1.206 0 2.262 0.231 3.169 0.706 0.9 0.469 1.644 1.1 2.225 1.887s0.994 1.694 1.25 2.706c0.256 1.012 0.344 2.069 0.275 3.175h-10.294c0 1.119 0.375 2.188 0.969 2.756zM27.156 15.188c-0.462-0.512-1.256-0.794-2.212-0.794-0.625 0-1.144 0.106-1.556 0.319-0.406 0.213-0.738 0.475-0.994 0.787-0.25 0.313-0.425 0.65-0.525 1.006-0.1 0.344-0.163 0.663-0.181 0.938h6.375c-0.094-1-0.438-1.738-0.906-2.256z">
                </path>
                <path d="M20.887 8h7.981v1.944h-7.981v-1.944z"></path>
            </symbol>

            <symbol id="icon-link" viewBox="0 0 32 32">
                <title>link</title>
                <path
                    d="M17.984 11.456c-0.704 0.704-0.704 1.856 0 2.56 2.112 2.112 2.112 5.568 0 7.68l-5.12 5.12c-2.048 2.048-5.632 2.048-7.68 0-1.024-1.024-1.6-2.4-1.6-3.84s0.576-2.816 1.6-3.84c0.704-0.704 0.704-1.856 0-2.56s-1.856-0.704-2.56 0c-1.696 1.696-2.624 3.968-2.624 6.368 0 2.432 0.928 4.672 2.656 6.4 1.696 1.696 3.968 2.656 6.4 2.656s4.672-0.928 6.4-2.656l5.12-5.12c3.52-3.52 3.52-9.248 0-12.8-0.736-0.672-1.888-0.672-2.592 0.032z">
                </path>
                <path
                    d="M29.344 2.656c-1.696-1.728-3.968-2.656-6.4-2.656s-4.672 0.928-6.4 2.656l-5.12 5.12c-3.52 3.52-3.52 9.248 0 12.8 0.352 0.352 0.8 0.544 1.28 0.544s0.928-0.192 1.28-0.544c0.704-0.704 0.704-1.856 0-2.56-2.112-2.112-2.112-5.568 0-7.68l5.12-5.12c2.048-2.048 5.632-2.048 7.68 0 1.024 1.024 1.6 2.4 1.6 3.84s-0.576 2.816-1.6 3.84c-0.704 0.704-0.704 1.856 0 2.56s1.856 0.704 2.56 0c1.696-1.696 2.656-3.968 2.656-6.4s-0.928-4.704-2.656-6.4z">
                </path>
            </symbol>
        </defs>
    </svg>
</body>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // Function to load all ratings
    function loadRatings() {
        $.ajax({
            url: 'ajax/get_rating.php', // Replace with your API endpoint
            method: 'GET',
            data: {
              worker_id:"<?=isset($_GET['id'])?$_GET['id']:''?>"
            },
            success: function(response) {
                $('#ratings-list').html(response); // Assuming your API returns HTML for ratings
            }
        });
    }

    // Load ratings on page load
    loadRatings();

    // Function to submit a new rating
   $('#submit-rating').click(function() {
        var rating = $('.rating .star.active').length;
        var comment = $('#comment').val();
        if(comment=='')
        {
            alert('please add comment..')
            return;
        }
        $.ajax({
            url: 'ajax/add_rating.php', // Replace with your API endpoint
            method: 'POST',
            data: {
                rating: rating,
                comment: comment,
                worker_id:"<?=isset($_GET['id'])?$_GET['id']:''?>"
            },
            success: function(response) {
                if (response.success) {
                    loadRatings(); // Reload ratings after adding
                    $('#comment').val(''); // Clear comment textarea
                } else {
                    alert('Failed to add rating.'); // Handle error
                }
            }
        });
    });

    // Toggle active state of stars on click
    $('.rating .star').click(function() {
        $(this).prevAll('.star').addBack().addClass('active');
        $(this).nextAll('.star').removeClass('active');
    });
});

// Service selection functions
function selectService(serviceId, serviceName, price) {
    // Remove previous selections
    $('.service-card').removeClass('selected');

    // Mark current service as selected
    $(`[data-service-id="${serviceId}"]`).addClass('selected');

    // Update hidden input
    $('#service_id').val(serviceId);

    // Update selected service info
    $('#selectedServiceInfo').html(`
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-1"><i class="fa-solid fa-check-circle text-success"></i> Selected Service</h5>
                <p class="mb-0">${serviceName}</p>
            </div>
            <div class="col-md-4 text-end">
                <h4 class="mb-0">₹${price}</h4>
            </div>
        </div>
    `);

    // Update summary
    $('#summaryServiceName').text(serviceName);
    $('#summaryPrice').text(`₹${price}`);

    // Show booking section with animation
    $('#bookingSection').slideDown(500);

    // Scroll to booking section
    $('html, body').animate({
        scrollTop: $('#bookingSection').offset().top - 100
    }, 800);

    // Update button text
    $('.btn-select-service').html('<i class="fa-solid fa-plus"></i> Select Service');
    $(`[data-service-id="${serviceId}"] .btn-select-service`).html('<i class="fa-solid fa-check"></i> Selected');
}

function cancelBooking() {
    // Hide booking section
    $('#bookingSection').slideUp(500);

    // Remove service selection
    $('.service-card').removeClass('selected');
    $('#service_id').val('');

    // Reset button text
    $('.btn-select-service').html('<i class="fa-solid fa-plus"></i> Select Service');

    // Scroll back to services
    $('html, body').animate({
        scrollTop: $('.services-section').offset().top - 100
    }, 800);
}

// Form validation
$('#bookingForm').on('submit', function(e) {
    if (!$('#service_id').val()) {
        e.preventDefault();
        Swal.fire({
            title: 'Service Required!',
            text: 'Please select a service before booking.',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        return false;
    }
});

// Auto-fill phone number if user is logged in
$(document).ready(function() {
    // You can add user's phone number here if available in session
    // $('#user_phone').val('<?php echo isset($_SESSION["user_phone"]) ? $_SESSION["user_phone"] : ""; ?>');
});
</script>

</html>