-- Create table for bulk booking/quotation requests
CREATE TABLE `tabl_bulk_booking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `cat_id` int(11) NOT NULL,
  `work_title` varchar(255) NOT NULL,
  `work_description` text NOT NULL,
  `required_workers` int(11) NOT NULL DEFAULT 1,
  `budget_per_worker` decimal(10,2) DEFAULT NULL,
  `total_budget` decimal(10,2) DEFAULT NULL,
  `required_experience` varchar(50) DEFAULT NULL,
  `work_duration` varchar(100) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `location_type` enum('single','multiple') DEFAULT 'single',
  `primary_address` text NOT NULL,
  `additional_addresses` text DEFAULT NULL,
  `city` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `pincode` varchar(10) NOT NULL,
  `contact_person` varchar(100) NOT NULL,
  `contact_phone` varchar(15) NOT NULL,
  `contact_email` varchar(100) DEFAULT NULL,
  `alternative_phone` varchar(15) DEFAULT NULL,
  `preferred_contact_time` varchar(50) DEFAULT NULL,
  `urgency_level` enum('low','medium','high','urgent') DEFAULT 'medium',
  `special_requirements` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=Pending, 1=Under Review, 2=Quoted, 3=Accepted, 4=In Progress, 5=Completed, 6=Cancelled',
  `admin_notes` text DEFAULT NULL,
  `quoted_amount` decimal(10,2) DEFAULT NULL,
  `quoted_workers` int(11) DEFAULT NULL,
  `quote_details` text DEFAULT NULL,
  `quote_date` datetime DEFAULT NULL,
  `accepted_date` datetime DEFAULT NULL,
  `completed_date` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `cat_id` (`cat_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add foreign key constraints (optional, depending on your existing structure)
-- ALTER TABLE `tabl_bulk_booking` ADD CONSTRAINT `fk_bulk_booking_user` FOREIGN KEY (`user_id`) REFERENCES `tabl_users`(`id`) ON DELETE CASCADE;
-- ALTER TABLE `tabl_bulk_booking` ADD CONSTRAINT `fk_bulk_booking_category` FOREIGN KEY (`cat_id`) REFERENCES `tabl_categories`(`cat_id`) ON DELETE CASCADE;
