<?php
// Test script for booking system functionality
require_once 'admin/lib/db_connection.php';

echo "<h2>Booking System Test Results</h2>";

// Test 1: Check if new table exists
echo "<h3>1. Database Table Check</h3>";
$tables_to_check = ['tabl_worker_bookings', 'tabl_service_order', 'tabl_worker_ratings'];

foreach($tables_to_check as $table) {
    $result = dbQuery("SHOW TABLES LIKE '$table'");
    if(mysqli_num_rows($result) > 0) {
        echo "✅ Table '$table' exists<br>";
    } else {
        echo "❌ Table '$table' missing<br>";
    }
}

// Test 2: Check table structure
echo "<h3>2. Table Structure Check</h3>";

// Check tabl_worker_bookings structure
echo "<h4>tabl_worker_bookings columns:</h4>";
$result = dbQuery("DESCRIBE tabl_worker_bookings");
if($result) {
    while($row = mysqli_fetch_assoc($result)) {
        echo "- {$row['Field']} ({$row['Type']})<br>";
    }
} else {
    echo "❌ Could not describe tabl_worker_bookings<br>";
}

// Check tabl_service_order updates
echo "<h4>tabl_service_order columns:</h4>";
$result = dbQuery("DESCRIBE tabl_service_order");
if($result) {
    $has_booking_notes = false;
    $has_completion_notes = false;
    $has_completed_at = false;
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "- {$row['Field']} ({$row['Type']})<br>";
        if($row['Field'] == 'booking_notes') $has_booking_notes = true;
        if($row['Field'] == 'completion_notes') $has_completion_notes = true;
        if($row['Field'] == 'completed_at') $has_completed_at = true;
    }
    
    echo $has_booking_notes ? "✅ booking_notes column exists<br>" : "❌ booking_notes column missing<br>";
    echo $has_completion_notes ? "✅ completion_notes column exists<br>" : "❌ completion_notes column missing<br>";
    echo $has_completed_at ? "✅ completed_at column exists<br>" : "❌ completed_at column missing<br>";
} else {
    echo "❌ Could not describe tabl_service_order<br>";
}

// Check tabl_worker_ratings updates
echo "<h4>tabl_worker_ratings columns:</h4>";
$result = dbQuery("DESCRIBE tabl_worker_ratings");
if($result) {
    $has_booking_id = false;
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "- {$row['Field']} ({$row['Type']})<br>";
        if($row['Field'] == 'booking_id') $has_booking_id = true;
    }
    
    echo $has_booking_id ? "✅ booking_id column exists<br>" : "❌ booking_id column missing<br>";
} else {
    echo "❌ Could not describe tabl_worker_ratings<br>";
}

// Test 3: Check file existence
echo "<h3>3. File Existence Check</h3>";
$files_to_check = [
    'me.php' => 'Worker profile page',
    'booking.php' => 'User booking page',
    'worker/my_bookings.php' => 'Worker booking management',
    'worker/ajax/update_booking_status.php' => 'Booking status update',
    'worker/ajax/complete_booking.php' => 'Booking completion',
    'ajax/add_rating.php' => 'Rating system',
    'database_migration.sql' => 'Database migration script',
    'BOOKING_SYSTEM_README.md' => 'Documentation'
];

foreach($files_to_check as $file => $description) {
    if(file_exists($file)) {
        echo "✅ $description ($file)<br>";
    } else {
        echo "❌ $description ($file) - Missing<br>";
    }
}

echo "<h3>4. System Status</h3>";
echo "<p>If all checks show ✅, your booking system is ready to use!</p>";
echo "<p>If any checks show ❌, please run the database migration script and ensure all files are in place.</p>";

echo "<h3>5. Next Steps</h3>";
echo "<ul>";
echo "<li>Run the database migration script if needed</li>";
echo "<li>Test worker profile booking functionality</li>";
echo "<li>Test worker booking management</li>";
echo "<li>Verify rating system restrictions</li>";
echo "<li>Check booking status workflow</li>";
echo "</ul>";
?>
