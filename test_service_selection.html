<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Selection Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        .service-card {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 10px;
            border-radius: 10px;
            cursor: pointer;
        }
        .service-card.selected {
            border-color: #28a745;
            background-color: #f8f9fa;
        }
        .btn-select-service {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        .booking-section {
            display: none;
            border: 1px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <h1>Service Selection Test</h1>
    
    <div class="services-grid">
        <div class="service-card" data-service-id="1" data-price="500" data-service-name="Hair Cut">
            <h5>Hair Cut</h5>
            <p>Professional hair cutting service</p>
            <div>₹500</div>
            <button type="button" class="btn-select-service">Select Service</button>
        </div>
        
        <div class="service-card" data-service-id="2" data-price="800" data-service-name="Skin Care">
            <h5>Skin Care</h5>
            <p>Complete skin care treatment</p>
            <div>₹800</div>
            <button type="button" class="btn-select-service">Select Service</button>
        </div>
    </div>
    
    <div class="booking-section" id="bookingSection">
        <h4>Book Your Service</h4>
        <div id="selectedServiceInfo"></div>
        <form id="bookingForm">
            <input type="hidden" id="service_id" name="service_id">
            <p>Service: <span id="summaryServiceName">-</span></p>
            <p>Price: <span id="summaryPrice">₹0</span></p>
            <button type="button" id="cancelBookingBtn">Cancel</button>
            <button type="submit">Book Service</button>
        </form>
    </div>

    <script>
    $(document).ready(function() {
        console.log('Test page loaded');
        console.log('Service cards found:', $('.service-card').length);
        console.log('Service buttons found:', $('.btn-select-service').length);
        
        // Service selection using event delegation
        $(document).on('click', '.btn-select-service', function(e) {
            e.preventDefault();
            console.log('Service button clicked');
            
            const serviceCard = $(this).closest('.service-card');
            const serviceId = serviceCard.data('service-id');
            const serviceName = serviceCard.data('service-name');
            const price = serviceCard.data('price');
            
            console.log('Service data:', {serviceId, serviceName, price});
            
            if (serviceId && serviceName && price) {
                selectService(serviceId, serviceName, price);
            } else {
                console.error('Missing service data');
            }
        });

        // Cancel booking button
        $('#cancelBookingBtn').on('click', function() {
            cancelBooking();
        });

        // Form validation
        $('#bookingForm').on('submit', function(e) {
            e.preventDefault();
            if (!$('#service_id').val()) {
                alert('Please select a service first!');
                return false;
            }
            alert('Booking submitted successfully!');
        });
    });

    function selectService(serviceId, serviceName, price) {
        console.log('selectService called with:', {serviceId, serviceName, price});
        
        // Remove previous selections
        $('.service-card').removeClass('selected');
        
        // Mark current service as selected
        $(`[data-service-id="${serviceId}"]`).addClass('selected');
        
        // Update hidden input
        $('#service_id').val(serviceId);
        
        // Update selected service info
        $('#selectedServiceInfo').html(`
            <div>
                <h5>Selected Service: ${serviceName}</h5>
                <p>Price: ₹${price}</p>
            </div>
        `);
        
        // Update summary
        $('#summaryServiceName').text(serviceName);
        $('#summaryPrice').text(`₹${price}`);
        
        // Show booking section
        $('#bookingSection').slideDown(500);
        
        // Update button text
        $('.btn-select-service').text('Select Service');
        $(`[data-service-id="${serviceId}"] .btn-select-service`).text('Selected');
    }

    function cancelBooking() {
        console.log('cancelBooking called');
        
        // Hide booking section
        $('#bookingSection').slideUp(500);
        
        // Remove service selection
        $('.service-card').removeClass('selected');
        $('#service_id').val('');
        
        // Reset button text
        $('.btn-select-service').text('Select Service');
    }
    </script>
</body>
</html>
