<?php
session_start();
require_once ('../../admin/lib/db_connection.php');
require_once ('../lib/auth.php');

header('Content-Type: application/json');

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['booking_id'])) {
    $worker_id = $_SESSION['worker_id'];
    $booking_id = intval($_POST['booking_id']);
    $completion_notes = isset($_POST['completion_notes']) ? $_POST['completion_notes'] : '';
    
    // Verify that this booking belongs to the logged-in worker
    $verifyQuery = dbQuery("SELECT * FROM tabl_worker_bookings WHERE id = '$booking_id' AND worker_id = '$worker_id'");
    
    if(mysqli_num_rows($verifyQuery) > 0) {
        $updateQuery = "UPDATE tabl_worker_bookings 
                       SET status = 2, 
                           completion_notes = '$completion_notes', 
                           completed_at = NOW(), 
                           updated_at = NOW() 
                       WHERE id = '$booking_id'";
        
        if(dbQuery($updateQuery)) {
            echo json_encode(["success" => true, "message" => "Booking marked as completed"]);
        } else {
            echo json_encode(["success" => false, "message" => "Failed to complete booking"]);
        }
    } else {
        echo json_encode(["success" => false, "message" => "Booking not found or unauthorized"]);
    }
} else {
    echo json_encode(["success" => false, "message" => "Invalid request"]);
}
?>
