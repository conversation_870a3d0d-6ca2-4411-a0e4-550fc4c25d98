<?php
session_start();
require_once ('../../admin/lib/db_connection.php');
require_once ('../lib/auth.php');

header('Content-Type: application/json');

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['booking_id']) && isset($_POST['status'])) {
    $worker_id = $_SESSION['worker_id'];
    $booking_id = intval($_POST['booking_id']);
    $status = intval($_POST['status']);
    
    // Verify that this booking belongs to the logged-in worker
    $verifyQuery = dbQuery("SELECT * FROM tabl_worker_bookings WHERE id = '$booking_id' AND worker_id = '$worker_id'");
    
    if(mysqli_num_rows($verifyQuery) > 0) {
        $updateQuery = "UPDATE tabl_worker_bookings SET status = '$status', updated_at = NOW() WHERE id = '$booking_id'";
        
        if(dbQuery($updateQuery)) {
            echo json_encode(["success" => true, "message" => "Booking status updated successfully"]);
        } else {
            echo json_encode(["success" => false, "message" => "Failed to update booking status"]);
        }
    } else {
        echo json_encode(["success" => false, "message" => "Booking not found or unauthorized"]);
    }
} else {
    echo json_encode(["success" => false, "message" => "Invalid request"]);
}
?>
