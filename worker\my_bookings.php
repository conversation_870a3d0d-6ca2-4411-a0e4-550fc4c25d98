<?php
require_once ('../admin/lib/db_connection.php');
require_once ('lib/auth.php');
$worker_id = $_SESSION['worker_id'];
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>GRS Worker</title>
    <link rel="stylesheet" href="assets/css/style.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH"
      crossorigin="anonymous"
    />
    <!--  -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />
    <!--  -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Baloo+Bhai+2:wght@400..800&display=swap"
      rel="stylesheet"
    />
    <!--  -->
  </head>
  <style>
    body {
      /* background-color: #eee; */
      font-family: "Baloo Bhai 2";
    }

    a {
      text-decoration: none;
    }

    .view-all {
      background-color: #2db79ffc;
      border-radius: 30px;
      color: #ffffff;
      font-size: 16px;
      font-weight: 600;
      padding: 7px 26px;
    }

    .right-content {
      margin-left: 17px;
    }

    .right-content p {
      margin-top: -6px;
      color: grey;
      margin-bottom: 5px;
      font-size: 15px;
    }

    .right-content h4 {
      font-size: 19px;
      color: black;
    }

    .input-group {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: stretch;
      width: 100%;
      box-shadow: 0px 2px 2px 0px #2c4d7c;
      /* font-weight: 600; */
    }

    .input-group-text {
      display: flex;
      align-items: center;
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.5;
      color: #ffffff;
      text-align: center;
      white-space: nowrap;
      background-color: #22b2a5;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
    }

    .form-select {
      font-weight: 500;
      color: #282b2f;
    }

    .bannnerimg {
      border-radius: 10px;
    }

    p {
      font-weight: 500;
    }

    /* .categoryicon {
        height: 100px;
    } */

    .img-fluid {
      height: 60px;
    }

    /* .card {
      border: 1px solid rgb(0, 153, 255);
      border-radius: 15px;
      box-shadow: 1px 1px 1px 1px #3368a4;
    } */

    .cattext {
      font-weight: 600;
    }

    .headingname {
      font-weight: 700;
      color: #303069;
      font-size: 25px;
    }

    /* Container for the category section */
    .category-section {
      overflow: auto;
    }

    /* Scrolling container */
    .category-scroll-container {
      display: flex;
      overflow-x: scroll;
      direction: rtl;
      padding: 10px;
      /* This causes the scroll direction to be from right to left */
    }

    /* Inner container for categories */
    .category-row {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-end;
      direction: ltr;
      /* Ensures the text within the categories is still left-to-right */
    }

    /* Styling for each category card */
    .category-card {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 10px;
      margin-right: 10px;
      border: 1px solid #ccc;
      border-radius: 8px;
    }

    /* Styling for the category icon */
    .category-icon {
      width: 50px;
      height: 50px;
    }

    .catgorydata {
      margin-left: 20px;
    }

    .imgdiv {
      box-shadow: 0px 0px 3px 1px #1d84f6;
      height: 89px;
      width: 88px;
      border-radius: 50%;
    }

    .carousel-container {
      position: relative;
    }

    .carousel-indicators {
      position: static;
      padding-top: 5px;
      /* Adjust as needed */
    }

    .carousel-indicators li {
      background-color: #204b69;
      /* Or any color you prefer */
      width: 10px;
      height: 10px;
      border-radius: 50%;
      display: inline-block;
      margin: 0 5px;
      /* Spacing between dots */
    }

    .carousel-indicators .active {
      background-color: #d4d0e6;
      /* Active indicator color */
    }

    .carousel-item img {
      border-radius: 39px 1px 45px 0px;
    }

    .btn-view {
      /* border: 1px solid #204b69; */
      color: #ffffff;
      font-size: 13px;
      font-weight: 700;
      border-radius: 71px;
      height: 32px;
      background-color: #6969b2;
      background-color: #182b3a;
      background-image: linear-gradient(315deg, #182b3a 0%, #1230b4 74%);
      border: none;
    }

    .imgdiv {
      box-shadow: 0px 0px 7px 1px #1230b4;
      height: 84px;
      width: 84px;
      border-radius: 50%;
      background-color: #fff;
    }

    a {
      color: black;
    }

    .tcolorone {
      color: black;
    }
    .card-text {
      margin-top: 4px;
      text-align: center;
      font-size: 85%;
    }
    .btn-white {
      background-color: #efefef;
      color: #f183f1;
      width: 60%;
    }
  </style>

  <body>
    <!-- header -->
    <nav class="horizontal-nav ">
        <div class="row align-items-center">
            <div class="col-4">
                <a href="account.php" id="nav-toggle-btn">
                    <i class="fa-solid fs-4 text-white fa-arrow-left"></i>
                </a>
            </div>
            <div class="col-8">
                <div class=" text-start text-white mt-2">
                    <h3>Bookings</h3>
                </div>
            </div>
        </div>
    </nav>
    <!-- /header -->
    <!-- carousel start-->

    <div class="mt-5">
      <div class="container">
        <h2><u>Your Bookings</u></h2>
      </div>
    </div>
    <div class="container">
        <!-- Service Bookings (Old System) -->
        <h3 class="mb-3">Service Bookings</h3>
        <?php
        $serviceBookingQueries = dbQuery("SELECT * FROM tabl_service_order WHERE worker_id='$worker_id' ORDER BY date DESC");
        if(mysqli_num_rows($serviceBookingQueries) > 0):
            while($row=dbFetchAssoc($serviceBookingQueries)):
                // getting service details
                $serviceId = $row['service_worker_id'];
                $serviceDetails = dbFetchAssoc(dbQuery("SELECT * FROM tabl_worker_services WHERE id='$serviceId'"));
                //user name
                $userId = $row['user_id'];
                $userDetails = dbFetchAssoc(dbQuery("SELECT * FROM tabl_users WHERE user_id ='$userId'"));
        ?>
        <div class="card mb-3">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5>Service Booking #<?=$row['id']?></h5>
                    </div>
                    <div class="col-md-4 text-end">
                        <?php
                        $statusLabels = [0 => 'Pending', 1 => 'Confirmed', 2 => 'Completed', 3 => 'Cancelled'];
                        $statusColors = [0 => 'warning', 1 => 'info', 2 => 'success', 3 => 'danger'];
                        ?>
                        <span class="badge bg-<?=$statusColors[$row['status']]?>"><?=$statusLabels[$row['status']]?></span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Customer Details</h6>
                        <p>
                            <strong>Name:</strong> <?=$userDetails['fullname']?><br>
                            <strong>Email:</strong> <?=$userDetails['email']?><br>
                            <strong>Phone:</strong> <?=$userDetails['phone_number']?><br>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>Service Details</h6>
                        <p>
                            <strong>Service:</strong> <?=isset($serviceDetails['service_name'])?$serviceDetails['service_name']:'N/A'?><br>
                            <strong>Price:</strong> <?=isset($serviceDetails['price'])?$serviceDetails['price']:'N/A'?> Rs.<br>
                            <strong>Visit Time:</strong> <?=date('d/m/Y h:i a', strtotime($row['visite_date_time']))?><br>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <?php endwhile; else: ?>
        <div class="alert alert-info">No service bookings found.</div>
        <?php endif; ?>

        <!-- Worker Profile Bookings (New System) -->
        <h3 class="mb-3 mt-4">Profile Bookings</h3>
        <?php
        $profileBookingQueries = dbQuery("SELECT wb.*, ws.service_name, ws.price as service_price, ws.servicesInclude
                                         FROM tabl_worker_bookings wb
                                         LEFT JOIN tabl_worker_services ws ON wb.service_id = ws.id
                                         WHERE wb.worker_id='$worker_id'
                                         ORDER BY wb.created_at DESC");
        if(mysqli_num_rows($profileBookingQueries) > 0):
            while($booking=dbFetchAssoc($profileBookingQueries)):
                $userId = $booking['user_id'];
                $userDetails = dbFetchAssoc(dbQuery("SELECT * FROM tabl_users WHERE user_id ='$userId'"));
        ?>
        <div class="card mb-3">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5>Booking #<?=$booking['id']?></h5>
                    </div>
                    <div class="col-md-4 text-end">
                        <?php
                        $statusLabels = [0 => 'Pending', 1 => 'Confirmed', 2 => 'Completed', 3 => 'Cancelled'];
                        $statusColors = [0 => 'warning', 1 => 'info', 2 => 'success', 3 => 'danger'];
                        ?>
                        <span class="badge bg-<?=$statusColors[$booking['status']]?>"><?=$statusLabels[$booking['status']]?></span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Customer Details</h6>
                        <p>
                            <strong>Name:</strong> <?=$userDetails['fullname']?><br>
                            <strong>Email:</strong> <?=$userDetails['email']?><br>
                            <strong>Phone:</strong> <?=$booking['user_phone']?><br>
                            <strong>Address:</strong> <?=$booking['user_address']?><br>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>Booking Details</h6>
                        <p>
                            <strong>Date:</strong> <?=date('d/m/Y', strtotime($booking['booking_date']))?><br>
                            <strong>Time:</strong> <?=date('h:i A', strtotime($booking['booking_time']))?><br>
                            <?php if($booking['service_name']): ?>
                            <strong>Selected Service:</strong> <?=$booking['service_name']?><br>
                            <strong>Service Price:</strong> ₹<?=$booking['service_price']?><br>
                            <strong>Service Includes:</strong> <?=$booking['servicesInclude']?><br>
                            <?php endif; ?>
                            <?php if($booking['service_description']): ?>
                            <strong>Additional Notes:</strong> <?=$booking['service_description']?><br>
                            <?php endif; ?>
                            <strong>Total Amount:</strong> ₹<?=$booking['total_price'] ?? $booking['service_price'] ?? 'N/A'?><br>
                            <strong>Booked on:</strong> <?=date('d/m/Y h:i A', strtotime($booking['created_at']))?><br>
                        </p>
                    </div>
                </div>

                <?php if($booking['status'] == 0): ?>
                <div class="mt-3">
                    <button class="btn btn-success btn-sm" onclick="updateBookingStatus(<?=$booking['id']?>, 1)">Confirm Booking</button>
                    <button class="btn btn-danger btn-sm" onclick="updateBookingStatus(<?=$booking['id']?>, 3)">Cancel Booking</button>
                </div>
                <?php elseif($booking['status'] == 1): ?>
                <div class="mt-3">
                    <button class="btn btn-primary btn-sm" onclick="markCompleted(<?=$booking['id']?>)">Mark as Completed</button>
                    <button class="btn btn-danger btn-sm" onclick="updateBookingStatus(<?=$booking['id']?>, 3)">Cancel Booking</button>
                </div>
                <?php endif; ?>

                <?php if($booking['worker_notes']): ?>
                <div class="mt-2">
                    <small><strong>Your Notes:</strong> <?=$booking['worker_notes']?></small>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endwhile; else: ?>
        <div class="alert alert-info">No profile bookings found.</div>
        <?php endif; ?>
    </div>
    <!-- carousel end -->
    <div class="p-5"></div>
    <!-- footer -->
    <?php require_once('inc/footer.php'); ?>
    <!-- /footer  -->

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.0.5/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script
      src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
      integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
      crossorigin="anonymous"
    ></script>
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
      crossorigin="anonymous"
    ></script>
    <script>
      function removeScrollBarPushing() {
        const offsetY = document.documentElement.scrollTop;
        let i = 0;
        const time = setInterval(function () {
          if (i++ < 2) {
            clearInterval(time);
          }
          document.documentElement.scrollTop = offsetY;
        }, 1);
      }

      // open sidenav
      document
        .getElementById("nav-toggle-btn")
        .addEventListener("click", function () {
          document.getElementById("sidenav").classList.add("show");
          removeScrollBarPushing();
        });
      // close sidenav
      document
        .querySelector("#sidenav .closebtn")
        .addEventListener("click", function () {
          document.getElementById("sidenav").classList.remove("show");
        });

      $(".hamburger").on("click", function () {
        $(this).parent().toggleClass("active");
      });
      function toggleSearchInput() {
        var searchInput = document.querySelector(".search-input");
        searchInput.style.display =
          searchInput.style.display === "none" ||
          searchInput.style.display === ""
            ? "block"
            : "none";
      }

      // Booking management functions
      function updateBookingStatus(bookingId, status) {
        if(confirm('Are you sure you want to update this booking status?')) {
          $.ajax({
            url: 'ajax/update_booking_status.php',
            method: 'POST',
            data: {
              booking_id: bookingId,
              status: status
            },
            success: function(response) {
              if(response.success) {
                location.reload();
              } else {
                alert('Failed to update booking status');
              }
            },
            error: function() {
              alert('Error updating booking status');
            }
          });
        }
      }

      function markCompleted(bookingId) {
        var notes = prompt('Add completion notes (optional):');
        $.ajax({
          url: 'ajax/complete_booking.php',
          method: 'POST',
          data: {
            booking_id: bookingId,
            completion_notes: notes || ''
          },
          success: function(response) {
            if(response.success) {
              alert('Booking marked as completed!');
              location.reload();
            } else {
              alert('Failed to mark booking as completed');
            }
          },
          error: function() {
            alert('Error completing booking');
          }
        });
      }
    </script>
  </body>
</html>
