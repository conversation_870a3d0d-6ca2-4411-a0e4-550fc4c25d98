<?php
/**
 * Worker Card Component
 * Displays worker information with enhanced pricing details
 * Usage: Include this file and call displayWorkerCard($workerData)
 */

function displayWorkerCard($worker) {
    $priceDisplay = getPriceDisplay($worker);
    $availabilityText = !empty($worker['availability']) ? $worker['availability'] : 'Contact for availability';
    
    echo '
    <div class="worker-card modern-card" data-worker-id="' . $worker['id'] . '">
        <div class="worker-image">
            <img src="' . getWorkerImage($worker) . '" 
                 alt="' . htmlspecialchars($worker['name']) . '"
                 onerror="this.src=\'assets/images/default-worker.jpg\'">
            ' . getFeaturedBadge($worker) . '
            ' . getVerifiedBadge($worker) . '
        </div>
        
        <div class="worker-info">
            <h5 class="worker-name">' . htmlspecialchars($worker['name']) . '</h5>
            <p class="worker-category">
                <i class="fa-solid fa-briefcase"></i> ' . htmlspecialchars($worker['cat_name']) . '
            </p>
            
            <div class="worker-location">
                <i class="fa-solid fa-map-marker-alt"></i> ' . htmlspecialchars($worker['city']) . '
            </div>
            
            <div class="worker-experience">
                <i class="fa-solid fa-medal"></i> ' . ($worker['experience'] ?? 'New') . ' Years Experience
            </div>
            
            ' . getPricingSection($worker) . '
            
            ' . getAvailabilitySection($worker) . '
            
            <div class="worker-stats">
                <div class="stat-item">
                    <span class="stat-value">' . number_format($worker['rating_avg'] ?? 0, 1) . '</span>
                    <span class="stat-label">Rating</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">' . ($worker['total_bookings'] ?? 0) . '</span>
                    <span class="stat-label">Bookings</span>
                </div>
            </div>
            
            <div class="worker-actions">
                <a href="me.php?id=' . $worker['id'] . '" class="btn btn-primary btn-view-profile">
                    <i class="fa-solid fa-eye"></i> View Profile
                </a>
                <button type="button" class="btn btn-success btn-book-now" 
                        onclick="quickBook(' . $worker['id'] . ')">
                    <i class="fa-solid fa-calendar-check"></i> Book Now
                </button>
            </div>
        </div>
    </div>';
}

function getPriceDisplay($worker) {
    if (empty($worker['price']) || $worker['price'] <= 0) {
        return 'Price on Request';
    }
    
    $price = number_format($worker['price'], 2);
    
    switch ($worker['price_type']) {
        case 'hourly':
            return '₹' . $price . '/hour';
        case 'fixed':
            return '₹' . $price . ' (Fixed)';
        default:
            return '₹' . $price;
    }
}

function getPricingSection($worker) {
    if (empty($worker['price']) || $worker['price'] <= 0) {
        return '
        <div class="pricing-section negotiable">
            <div class="price-display">Price on Request</div>
            <small class="price-note">Contact for pricing</small>
        </div>';
    }
    
    $priceDisplay = getPriceDisplay($worker);
    $minHoursText = '';
    
    if ($worker['price_type'] === 'hourly' && !empty($worker['min_service_hours'])) {
        $minHoursText = '<small class="min-hours">Min ' . $worker['min_service_hours'] . ' hours</small>';
    }
    
    return '
    <div class="pricing-section">
        <div class="price-display">' . $priceDisplay . '</div>
        ' . $minHoursText . '
    </div>';
}

function getAvailabilitySection($worker) {
    if (empty($worker['availability'])) {
        return '';
    }
    
    return '
    <div class="availability-section">
        <i class="fa-solid fa-clock"></i>
        <span>' . htmlspecialchars($worker['availability']) . '</span>
    </div>';
}

function getWorkerImage($worker) {
    if (!empty($worker['worker_image']) && file_exists('worker/assets/images/workers/workers-img/' . $worker['worker_image'])) {
        return 'worker/assets/images/workers/workers-img/' . $worker['worker_image'];
    }
    return 'assets/images/default-worker.jpg';
}

function getFeaturedBadge($worker) {
    if (!empty($worker['featured']) && $worker['featured'] == 1) {
        return '<div class="featured-badge"><i class="fa-solid fa-star"></i> Featured</div>';
    }
    return '';
}

function getVerifiedBadge($worker) {
    if (!empty($worker['background_verified']) && $worker['background_verified'] == 1) {
        return '<div class="verified-badge"><i class="fa-solid fa-shield-check"></i> Verified</div>';
    }
    return '';
}

// CSS Styles for Worker Cards
function getWorkerCardStyles() {
    return '
    <style>
    .worker-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        margin-bottom: 20px;
        position: relative;
    }
    
    .worker-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    }
    
    .worker-image {
        height: 200px;
        overflow: hidden;
        position: relative;
    }
    
    .worker-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .featured-badge, .verified-badge {
        position: absolute;
        top: 10px;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        color: white;
    }
    
    .featured-badge {
        right: 10px;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .verified-badge {
        left: 10px;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .worker-info {
        padding: 20px;
    }
    
    .worker-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 8px;
    }
    
    .worker-category, .worker-location, .worker-experience {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .pricing-section {
        margin: 15px 0;
        padding: 10px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-radius: 8px;
        text-align: center;
    }
    
    .price-display {
        font-size: 1.4rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 5px;
    }
    
    .min-hours, .price-note {
        font-size: 0.8rem;
        color: #666;
    }
    
    .availability-section {
        margin: 10px 0;
        padding: 8px 12px;
        background: rgba(79, 172, 254, 0.1);
        border-radius: 6px;
        font-size: 0.85rem;
        color: #4facfe;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .worker-stats {
        display: flex;
        justify-content: space-around;
        margin: 15px 0;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-value {
        display: block;
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: #7f8c8d;
    }
    
    .worker-actions {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }
    
    .btn-view-profile, .btn-book-now {
        flex: 1;
        padding: 10px;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .btn-view-profile {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }
    
    .btn-book-now {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border: none;
    }
    
    .btn-view-profile:hover, .btn-book-now:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    </style>';
}

// JavaScript for quick booking
function getWorkerCardScripts() {
    return '
    <script>
    function quickBook(workerId) {
        window.location.href = "me.php?id=" + workerId + "#bookingSection";
    }
    </script>';
}
?>

<!-- Usage Example:
<?php
// Include this file in your worker listing pages
// require_once('worker_card_component.php');

// Echo styles once per page
// echo getWorkerCardStyles();

// Display worker cards
// while($worker = dbFetchAssoc($workersQuery)) {
//     displayWorkerCard($worker);
// }

// Echo scripts once per page
// echo getWorkerCardScripts();
?>
-->
