<?php
require_once('admin/lib/db_connection.php');
require_once('lib/auth.php');

// Get search parameters
$searchKeyword = $_GET['search'] ?? '';
$categoryId = $_GET['category'] ?? '';
$stateId = $_GET['state'] ?? '';
$cityName = $_GET['city'] ?? '';
$pincode = $_GET['pincode'] ?? '';
$page = $_GET['page'] ?? 1;
$limit = 12;
$offset = ($page - 1) * $limit;

// Build search query
$whereConditions = ["w.status = 1", "wc.cat_status = 1", "wc.status = 1", "w.is_deleted = 0"];

if (!empty($searchKeyword)) {
    $whereConditions[] = "(w.name LIKE '%$searchKeyword%' OR c.cat_name LIKE '%$searchKeyword%' OR wc.description LIKE '%$searchKeyword%')";
}

if (!empty($categoryId)) {
    $whereConditions[] = "wc.cat_id = '$categoryId'";
}

if (!empty($stateId)) {
    $whereConditions[] = "w.state = '$stateId'";
}

if (!empty($cityName)) {
    $whereConditions[] = "w.city LIKE '%$cityName%'";
}

if (!empty($pincode)) {
    $whereConditions[] = "w.pincode = '$pincode'";
}

$whereClause = implode(' AND ', $whereConditions);

// Main query
$query = "SELECT wc.id, wc.worker_image, wc.work_image, wc.experience, wc.price, wc.price_type, wc.availability, wc.min_service_hours,
                 w.name, w.phone, w.city, w.state, w.pincode, wc.worker_id, wc.cat_id, wc.description,
                 c.cat_name, c.cat_icon
          FROM tabl_workers_category wc
          INNER JOIN tabl_workers w ON wc.worker_id = w.worker_id
          INNER JOIN tabl_categories c ON wc.cat_id = c.cat_id
          WHERE $whereClause
          ORDER BY wc.id DESC
          LIMIT $limit OFFSET $offset";

$result = dbQuery($query);

// Count total results
$countQuery = "SELECT COUNT(*) as total
               FROM tabl_workers_category wc
               INNER JOIN tabl_workers w ON wc.worker_id = w.worker_id
               INNER JOIN tabl_categories c ON wc.cat_id = c.cat_id
               WHERE $whereClause";
$countResult = dbQuery($countQuery);
$totalResults = dbFetchAssoc($countResult)['total'];
$totalPages = ceil($totalResults / $limit);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Worker Profiles - GRS Worker</title>
    <link rel="stylesheet" href="assets/css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baloo+Bhai+2:wght@400..800&display=swap" rel="stylesheet">
</head>

<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: "Baloo Bhai 2";
        min-height: 100vh;
    }

    a {
        text-decoration: none;
    }

    /* Modern Page Header */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        text-align: center;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
    }

    /* Search Section */
    .search-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .search-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .form-control,
    .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-search {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        height: fit-content;
    }

    .btn-search:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-clear {
        background: #6c757d;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        height: fit-content;
    }

    .btn-clear:hover {
        background: #5a6268;
        color: white;
    }

    /* Results Section */
    .results-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .results-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
    }

    .results-count {
        color: #7f8c8d;
        font-weight: 500;
    }

    /* Worker Cards Grid */
    .workers-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .worker-card-modern {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        position: relative;
        border: 2px solid transparent;
    }

    .worker-card-modern:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        border-color: #667eea;
    }

    .worker-image-container {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .worker-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .worker-card-modern:hover .worker-image {
        transform: scale(1.05);
    }

    .worker-overlay {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .worker-rating {
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .worker-rating i {
        color: #ffd700;
    }

    .worker-details {
        padding: 1.5rem;
    }

    .worker-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .worker-category {
        color: #667eea;
        font-size: 0.95rem;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .worker-info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .worker-location,
    .worker-experience {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.85rem;
        color: #7f8c8d;
    }

    .worker-price {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        padding: 12px;
        border-radius: 12px;
        text-align: center;
        margin-bottom: 1rem;
    }

    .price-label {
        display: block;
        font-size: 0.8rem;
        color: #7f8c8d;
        margin-bottom: 4px;
    }

    .price-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #667eea;
    }

    .worker-actions {
        padding: 0 1.5rem 1.5rem;
    }

    .btn-view-profile {
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-view-profile:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    /* Pagination */
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }

    .pagination {
        display: flex;
        gap: 0.5rem;
    }

    .page-link {
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .page-link:hover,
    .page-link.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    /* No Results */
    .no-results {
        text-align: center;
        padding: 3rem;
        color: #7f8c8d;
    }

    .no-results i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #bdc3c7;
    }

    .no-results h3 {
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    /* Responsive Design */

    /* XL screens (1200px and up) - 4 columns */
    @media (min-width: 1200px) {
        .workers-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    /* LG screens (992px to 1199px) - 3 columns */
    @media (max-width: 1199px) and (min-width: 992px) {
        .workers-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    /* MD screens (768px to 991px) - 2 columns */
    @media (max-width: 991px) and (min-width: 768px) {
        .workers-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }

        .search-form {
            grid-template-columns: repeat(2, 1fr);
        }

        .results-header {
            flex-direction: column;
            align-items: flex-start;
        }
    }

    /* SM and below (767px and down) - 1 column */
    @media (max-width: 767px) {
        .page-title {
            font-size: 2rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .search-form {
            grid-template-columns: 1fr;
        }

        .results-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .workers-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Extra small screens (480px and down) */
    @media (max-width: 480px) {
        .page-header {
            padding: 2rem 0;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .search-section,
        .results-section {
            padding: 1rem;
        }

        .worker-details {
            padding: 1rem;
        }

        .worker-actions {
            padding: 0 1rem 1rem;
        }

        .workers-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
    }
</style>

<body>
    <!-- header -->
    <?= require_once('inc/header.php'); ?>
    <!-- /header -->

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fa-solid fa-users"></i>
                Worker Profiles
            </h1>
            <p class="page-subtitle">Find skilled professionals for your needs</p>
        </div>
    </div>

    <div class="container pb-5">
        <!-- Search Section -->
        <div class="search-section">
            <form method="GET" action="" class="search-form">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fa-solid fa-search"></i> Search by Name/Skill
                    </label>
                    <input type="text" name="search" class="form-control"
                        placeholder="Enter worker name or skill..."
                        value="<?= htmlspecialchars($searchKeyword) ?>">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fa-solid fa-th-large"></i> Category
                    </label>
                    <select name="category" class="form-select">
                        <option value="">All Categories</option>
                        <?php
                        $catQuery = dbQuery("SELECT cat_id, cat_name FROM tabl_categories WHERE cat_status=1 ORDER BY cat_name");
                        while ($cat = dbFetchAssoc($catQuery)) {
                            $selected = ($categoryId == $cat['cat_id']) ? 'selected' : '';
                            echo "<option value='{$cat['cat_id']}' $selected>{$cat['cat_name']}</option>";
                        }
                        ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fa-solid fa-map-marker-alt"></i> State
                    </label>
                    <select name="state" class="form-select" id="stateSelect">
                        <option value="">All States</option>
                        <?php
                        $stateQuery = dbQuery("SELECT state_id, state_name FROM tabl_states ORDER BY state_name");
                        while ($state = dbFetchAssoc($stateQuery)) {
                            $selected = ($stateId == $state['state_id']) ? 'selected' : '';
                            echo "<option value='{$state['state_id']}' $selected>{$state['state_name']}</option>";
                        }
                        ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fa-solid fa-city"></i> City
                    </label>
                    <input type="text" name="city" class="form-control"
                        placeholder="Enter city name..."
                        value="<?= htmlspecialchars($cityName) ?>">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fa-solid fa-map-pin"></i> Pincode
                    </label>
                    <input type="text" name="pincode" class="form-control"
                        placeholder="Enter pincode..."
                        value="<?= htmlspecialchars($pincode) ?>">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-search">
                        <i class="fa-solid fa-search"></i> Search
                    </button>
                </div>

                <div class="form-group">
                    <a href="worker_profiles.php" class="btn btn-clear">
                        <i class="fa-solid fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>

        <!-- Results Section -->
        <div class="results-section">
            <div class="results-header">
                <h2 class="results-title">
                    <i class="fa-solid fa-list"></i> Search Results
                </h2>
                <div class="results-count">
                    <?php if ($totalResults > 0): ?>
                        Showing <?= (($page - 1) * $limit) + 1 ?> - <?= min($page * $limit, $totalResults) ?> of <?= $totalResults ?> workers
                    <?php else: ?>
                        No workers found
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($totalResults > 0): ?>
                <div class="workers-grid">
                    <?php while ($worker = dbFetchAssoc($result)):
                        // Get rating data
                        $ratingQuery = dbQuery("SELECT AVG(rating) as avg_rating, COUNT(*) as total_ratings
                                              FROM tabl_worker_ratings WHERE worker_id = " . $worker['id']);
                        $ratingData = dbFetchAssoc($ratingQuery);
                        $avgRating = round($ratingData['avg_rating'] ?? 0, 1);
                        $totalRatings = $ratingData['total_ratings'] ?? 0;

                        // Format price display
                        $priceDisplay = 'Price on Request';
                        if (!empty($worker['price']) && $worker['price'] > 0) {
                            if ($worker['price_type'] === 'hourly') {
                                $priceDisplay = '₹' . number_format($worker['price'], 0) . '/hr';
                                if (!empty($worker['min_service_hours'])) {
                                    $priceDisplay .= ' (Min ' . $worker['min_service_hours'] . 'h)';
                                }
                            } elseif ($worker['price_type'] === 'fixed') {
                                $priceDisplay = '₹' . number_format($worker['price'], 0) . ' (Fixed)';
                            } else {
                                $priceDisplay = '₹' . number_format($worker['price'], 0);
                            }
                        }

                        // Get state name
                        $stateName = '';
                        if (!empty($worker['state'])) {
                            $stateQuery = dbQuery("SELECT state_name FROM tabl_states WHERE state_id = '" . $worker['state'] . "'");
                            $stateData = dbFetchAssoc($stateQuery);
                            $stateName = $stateData['state_name'] ?? '';
                        }
                    ?>
                        <div class="worker-card-modern">
                            <div class="worker-image-container">
                                <img src="worker/assets/images/workers/workers-img/<?= $worker['worker_image'] ?>"
                                    class="worker-image"
                                    onerror="this.src='assets/images/profile.jpg'"
                                    alt="<?= $worker['name'] ?>">
                                <div class="worker-overlay">
                                    <div class="worker-rating">
                                        <i class="fa-solid fa-star"></i>
                                        <span><?= $avgRating ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="worker-details">
                                <h5 class="worker-name"><?= $worker['name'] ?></h5>
                                <p class="worker-category">
                                    <i class="fa-solid fa-briefcase"></i>
                                    <?= $worker['cat_name'] ?>
                                </p>

                                <div class="worker-info-row">
                                    <div class="worker-location">
                                        <i class="fa-solid fa-map-marker-alt"></i>
                                        <span><?= $worker['city'] ?><?= $stateName ? ', ' . $stateName : '' ?></span>
                                    </div>
                                    <div class="worker-experience">
                                        <i class="fa-solid fa-medal"></i>
                                        <span><?= $worker['experience'] ?? '0' ?>Y Exp</span>
                                    </div>
                                </div>

                                <?php if (!empty($worker['availability'])): ?>
                                    <div class="worker-info-row">
                                        <div class="worker-location">
                                            <i class="fa-solid fa-clock"></i>
                                            <span><?= $worker['availability'] ?></span>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <div class="worker-price">
                                    <span class="price-label">Starting at</span>
                                    <span class="price-value"><?= $priceDisplay ?></span>
                                </div>

                                <?php if (!empty($worker['description'])): ?>
                                    <div class="worker-description">
                                        <p style="font-size: 0.9rem; color: #7f8c8d; margin-bottom: 1rem;">
                                            <?= substr($worker['description'], 0, 100) ?><?= strlen($worker['description']) > 100 ? '...' : '' ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="worker-actions">
                                <a href="me.php?id=<?= $worker['id'] ?>" class="btn btn-view-profile">
                                    <i class="fa-solid fa-eye"></i> View Profile & Book
                                </a>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="pagination-wrapper">
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" class="page-link">
                                    <i class="fa-solid fa-chevron-left"></i> Previous
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                                    class="page-link <?= $i == $page ? 'active' : '' ?>">
                                    <?= $i ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" class="page-link">
                                    Next <i class="fa-solid fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

            <?php else: ?>
                <div class="no-results">
                    <i class="fa-solid fa-users-slash"></i>
                    <h3>No Workers Found</h3>
                    <p>Try adjusting your search criteria or browse all categories.</p>
                    <a href="worker_profiles.php" class="btn btn-search mt-3">
                        <i class="fa-solid fa-refresh"></i> View All Workers
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="py-4"></div>


    <!-- footer -->
    <?php require_once('inc/footer.php'); ?>
    <!-- /footer -->

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
        integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"
        integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy"
        crossorigin="anonymous"></script>

    <script>
        // Auto-submit form when state changes to load cities
        $('#stateSelect').on('change', function() {
            // You can add AJAX functionality here to load cities based on state
            // For now, we'll keep it simple
        });

        // Smooth scroll for better UX
        $('a[href^="#"]').on('click', function(event) {
            var target = $(this.getAttribute('href'));
            if (target.length) {
                event.preventDefault();
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - 100
                }, 1000);
            }
        });

        // Add loading state to search button
        $('form').on('submit', function() {
            $('.btn-search').html('<i class="fa-solid fa-spinner fa-spin"></i> Searching...');
        });
    </script>
</body>

</html>